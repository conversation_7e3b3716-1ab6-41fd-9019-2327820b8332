from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import random
import copy
import json
import time
from dataclasses import dataclass, asdict
from typing import List, Dict
import numpy as np

@dataclass
class IntelligenceGenome:
    """Genome for evolving AI intelligence - includes model selection and advanced parameters"""
    # Model selection (intelligence level)
    model_name: str = "gpt2"
    model_size: str = "small"  # small, medium, large, xl
    
    # Advanced generation parameters
    temperature: float = 0.7
    top_k: int = 50
    top_p: float = 0.95
    repetition_penalty: float = 1.1
    length_penalty: float = 1.0
    diversity_penalty: float = 0.0
    
    # Response sophistication
    max_response_length: int = 50
    min_response_length: int = 10
    num_beams: int = 1  # Beam search for better quality
    do_sample: bool = True
    
    # Advanced reasoning parameters
    context_window: int = 512
    reasoning_depth: int = 1  # How many "thinking" steps
    
    # Intelligence metrics
    intelligence_score: float = 0.0
    complexity_score: float = 0.0
    coherence_score: float = 0.0
    knowledge_score: float = 0.0
    
    # Evolution metadata
    fitness: float = 0.0
    generation: int = 0
    id: str = ""

class IntelligenceEvolution:
    """Evolution system focused on increasing AI intelligence"""
    
    def __init__(self, population_size: int = 8):
        self.population_size = population_size
        self.population: List[IntelligenceGenome] = []
        self.generation = 0
        
        # Available models (ordered by intelligence/size)
        self.model_hierarchy = [
            {"name": "gpt2", "size": "small", "intelligence_level": 1},
            {"name": "gpt2-medium", "size": "medium", "intelligence_level": 2},
            {"name": "gpt2-large", "size": "large", "intelligence_level": 3},
            {"name": "microsoft/DialoGPT-medium", "size": "medium", "intelligence_level": 2},
            {"name": "microsoft/DialoGPT-large", "size": "large", "intelligence_level": 3},
        ]
        
        # Setup CUDA
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Intelligence Evolution using device: {self.device}")
        
        # Model cache to avoid reloading
        self.model_cache = {}
        
    def load_model(self, model_name: str):
        """Load and cache models"""
        if model_name not in self.model_cache:
            print(f"Loading intelligence model: {model_name}")
            
            try:
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                
                if self.device.type == "cuda":
                    model = AutoModelForCausalLM.from_pretrained(
                        model_name,
                        dtype=torch.float16,
                        device_map="auto"
                    )
                else:
                    model = AutoModelForCausalLM.from_pretrained(model_name)
                    model = model.to(self.device)
                
                if tokenizer.pad_token is None:
                    tokenizer.pad_token = tokenizer.eos_token
                
                self.model_cache[model_name] = {"model": model, "tokenizer": tokenizer}
                print(f"Model {model_name} loaded and cached")
                
            except Exception as e:
                print(f"Failed to load {model_name}: {e}")
                # Fallback to GPT-2
                if model_name != "gpt2":
                    return self.load_model("gpt2")
                else:
                    raise e
        
        return self.model_cache[model_name]
    
    def create_initial_population(self):
        """Create initial population with varying intelligence levels"""
        print("Creating initial intelligence population...")
        
        for i in range(self.population_size):
            # Start with different model sizes
            model_info = random.choice(self.model_hierarchy[:3])  # Start with smaller models
            
            genome = IntelligenceGenome(
                model_name=model_info["name"],
                model_size=model_info["size"],
                temperature=random.uniform(0.3, 1.2),
                top_k=random.randint(20, 100),
                top_p=random.uniform(0.7, 0.99),
                repetition_penalty=random.uniform(1.0, 1.5),
                length_penalty=random.uniform(0.8, 1.2),
                diversity_penalty=random.uniform(0.0, 0.3),
                max_response_length=random.randint(30, 100),
                min_response_length=random.randint(5, 25),
                num_beams=random.choice([1, 2, 3]),
                do_sample=random.choice([True, False]),
                context_window=random.choice([256, 512, 768]),
                reasoning_depth=random.randint(1, 3),
                generation=0,
                id=f"gen0_intelligence{i}"
            )
            
            self.population.append(genome)
        
        print("Intelligence population created!")
    
    def generate_intelligent_response(self, genome: IntelligenceGenome, user_input: str) -> str:
        """Generate response using advanced intelligence parameters"""
        model_data = self.load_model(genome.model_name)
        model = model_data["model"]
        tokenizer = model_data["tokenizer"]
        
        # Advanced prompting for intelligence
        if genome.reasoning_depth > 1:
            prompt = f"Think step by step about this question: {user_input}\n\nThought process:\n1. Let me analyze this question...\n2. The key aspects are...\n3. My response:\n\nHuman: {user_input}\nAssistant:"
        else:
            prompt = f"Human: {user_input}\nAssistant:"
        
        # Encode with context window limit
        input_ids = tokenizer.encode(prompt, return_tensors="pt").to(self.device)
        
        if input_ids.shape[-1] > genome.context_window:
            input_ids = input_ids[:, -genome.context_window:]
        
        # Advanced generation with intelligence parameters
        generation_kwargs = {
            "input_ids": input_ids,
            "max_length": input_ids.shape[-1] + genome.max_response_length,
            "min_length": input_ids.shape[-1] + genome.min_response_length,
            "temperature": genome.temperature,
            "top_k": genome.top_k,
            "top_p": genome.top_p,
            "repetition_penalty": genome.repetition_penalty,
            "length_penalty": genome.length_penalty,
            "diversity_penalty": genome.diversity_penalty,
            "do_sample": genome.do_sample,
            "pad_token_id": tokenizer.eos_token_id,
            "attention_mask": torch.ones(input_ids.shape, device=self.device)
        }
        
        # Use beam search for higher intelligence
        if genome.num_beams > 1:
            generation_kwargs["num_beams"] = genome.num_beams
            generation_kwargs["early_stopping"] = True
        
        with torch.no_grad():
            output = model.generate(**generation_kwargs)
        
        # Extract response
        response = tokenizer.decode(output[0], skip_special_tokens=True)
        
        if "Assistant:" in response:
            bot_response = response.split("Assistant:")[-1].strip()
            if "Human:" in bot_response:
                bot_response = bot_response.split("Human:")[0].strip()
        else:
            bot_response = "I need to think more about this."
        
        return bot_response.strip()
    
    def evaluate_intelligence(self, genome: IntelligenceGenome, test_questions: List[Dict]) -> Dict[str, float]:
        """Evaluate AI intelligence across multiple dimensions"""
        scores = {
            "intelligence": 0.0,
            "complexity": 0.0,
            "coherence": 0.0,
            "knowledge": 0.0
        }
        
        for question_data in test_questions:
            question = question_data["question"]
            category = question_data["category"]
            difficulty = question_data["difficulty"]
            
            try:
                response = self.generate_intelligent_response(genome, question)
                
                # Intelligence scoring
                intelligence_score = self.score_intelligence(response, question, difficulty)
                complexity_score = self.score_complexity(response)
                coherence_score = self.score_coherence(response)
                knowledge_score = self.score_knowledge(response, category)
                
                # Weight by difficulty
                weight = difficulty / 5.0
                scores["intelligence"] += intelligence_score * weight
                scores["complexity"] += complexity_score * weight
                scores["coherence"] += coherence_score * weight
                scores["knowledge"] += knowledge_score * weight
                
            except Exception as e:
                print(f"Error evaluating {genome.id}: {e}")
                continue
        
        # Average scores
        num_questions = len(test_questions)
        for key in scores:
            scores[key] /= num_questions
        
        return scores
    
    def score_intelligence(self, response: str, question: str, difficulty: int) -> float:
        """Score the intelligence of a response"""
        if not response or len(response.strip()) < 5:
            return 0.0
        
        score = 0.5  # Base score
        
        # Length and detail (more intelligent responses are often more detailed)
        if len(response) > 50:
            score += 0.2
        if len(response) > 100:
            score += 0.1
        
        # Vocabulary sophistication
        sophisticated_words = ["analyze", "consider", "perspective", "complex", "nuanced", 
                             "furthermore", "however", "therefore", "consequently", "specifically"]
        word_count = sum(1 for word in sophisticated_words if word in response.lower())
        score += min(0.2, word_count * 0.05)
        
        # Avoid simple/dumb responses
        simple_responses = ["i don't know", "maybe", "i think", "probably", "i'm not sure"]
        if any(simple in response.lower() for simple in simple_responses):
            score -= 0.2
        
        # Bonus for question-specific intelligence
        if "?" in question and "because" in response.lower():
            score += 0.1
        
        return min(1.0, max(0.0, score))
    
    def score_complexity(self, response: str) -> float:
        """Score the complexity of reasoning in response"""
        if not response:
            return 0.0
        
        # Sentence structure complexity
        sentences = response.split('.')
        avg_sentence_length = np.mean([len(s.split()) for s in sentences if s.strip()])
        
        complexity_score = min(1.0, avg_sentence_length / 15.0)
        
        # Complex reasoning indicators
        reasoning_words = ["because", "therefore", "however", "although", "furthermore", 
                          "consequently", "nevertheless", "moreover", "specifically"]
        reasoning_count = sum(1 for word in reasoning_words if word in response.lower())
        complexity_score += min(0.3, reasoning_count * 0.1)
        
        return min(1.0, complexity_score)
    
    def score_coherence(self, response: str) -> float:
        """Score the coherence and grammatical correctness"""
        if not response:
            return 0.0
        
        score = 0.5
        
        # Proper sentence endings
        if response.strip().endswith(('.', '!', '?')):
            score += 0.2
        
        # No truncated responses
        if not response.endswith(('...', ' and', ' but', ' or')):
            score += 0.2
        
        # Reasonable length
        if 10 <= len(response) <= 200:
            score += 0.1
        
        return min(1.0, score)
    
    def score_knowledge(self, response: str, category: str) -> float:
        """Score domain knowledge demonstration"""
        if not response:
            return 0.0
        
        # Category-specific knowledge indicators
        knowledge_indicators = {
            "science": ["research", "study", "evidence", "theory", "experiment", "data"],
            "technology": ["system", "algorithm", "process", "development", "innovation"],
            "philosophy": ["concept", "idea", "perspective", "argument", "reasoning"],
            "general": ["understand", "explain", "example", "important", "consider"]
        }
        
        indicators = knowledge_indicators.get(category, knowledge_indicators["general"])
        knowledge_count = sum(1 for word in indicators if word in response.lower())
        
        return min(1.0, knowledge_count * 0.2)
    
    def evolve_intelligence(self, test_questions: List[Dict], num_generations: int = 5):
        """Evolve AI intelligence over generations"""
        print(f"Starting intelligence evolution for {num_generations} generations...")
        
        for generation in range(num_generations):
            print(f"\n🧠 INTELLIGENCE GENERATION {generation + 1}")
            
            # Evaluate intelligence
            for i, genome in enumerate(self.population):
                print(f"Evaluating intelligence {i+1}/{len(self.population)}: {genome.id}")
                
                scores = self.evaluate_intelligence(genome, test_questions)
                
                # Update genome scores
                genome.intelligence_score = scores["intelligence"]
                genome.complexity_score = scores["complexity"]
                genome.coherence_score = scores["coherence"]
                genome.knowledge_score = scores["knowledge"]
                
                # Combined fitness (weighted)
                genome.fitness = (scores["intelligence"] * 0.4 + 
                                scores["complexity"] * 0.2 + 
                                scores["coherence"] * 0.2 + 
                                scores["knowledge"] * 0.2)
                
                print(f"  Intelligence: {scores['intelligence']:.3f}, "
                      f"Complexity: {scores['complexity']:.3f}, "
                      f"Coherence: {scores['coherence']:.3f}, "
                      f"Knowledge: {scores['knowledge']:.3f}, "
                      f"Overall: {genome.fitness:.3f}")
            
            # Show generation stats
            fitnesses = [g.fitness for g in self.population]
            intelligence_scores = [g.intelligence_score for g in self.population]
            
            print(f"\nGeneration {generation + 1} Results:")
            print(f"  Best Fitness: {max(fitnesses):.3f}")
            print(f"  Avg Fitness: {np.mean(fitnesses):.3f}")
            print(f"  Best Intelligence: {max(intelligence_scores):.3f}")
            print(f"  Avg Intelligence: {np.mean(intelligence_scores):.3f}")
            
            # Evolve to next generation
            if generation < num_generations - 1:
                self.create_next_intelligence_generation()
        
        return self.get_smartest_ai()
    
    def create_next_intelligence_generation(self):
        """Create next generation with intelligence-focused evolution"""
        # Sort by fitness
        self.population.sort(key=lambda x: x.fitness, reverse=True)
        
        new_population = []
        
        # Elite preservation (top 25%)
        elite_count = max(1, self.population_size // 4)
        for i in range(elite_count):
            elite = copy.deepcopy(self.population[i])
            elite.generation = self.generation + 1
            elite.id = f"gen{self.generation + 1}_elite{i}"
            new_population.append(elite)
        
        # Intelligence-focused reproduction
        while len(new_population) < self.population_size:
            # Select intelligent parents
            parent1 = self.select_intelligent_parent()
            parent2 = self.select_intelligent_parent()
            
            # Create intelligent offspring
            child = self.intelligent_crossover(parent1, parent2)
            child = self.intelligent_mutation(child)
            
            child.generation = self.generation + 1
            child.id = f"gen{self.generation + 1}_smart{len(new_population)}"
            child.fitness = 0.0
            
            new_population.append(child)
        
        self.population = new_population
        self.generation += 1
    
    def select_intelligent_parent(self) -> IntelligenceGenome:
        """Select parent based on intelligence metrics"""
        # Tournament selection weighted by intelligence
        tournament_size = 3
        tournament = random.sample(self.population, min(tournament_size, len(self.population)))
        
        # Weight selection by intelligence score
        return max(tournament, key=lambda x: x.intelligence_score * 0.6 + x.fitness * 0.4)
    
    def intelligent_crossover(self, parent1: IntelligenceGenome, parent2: IntelligenceGenome) -> IntelligenceGenome:
        """Crossover focused on preserving intelligence"""
        child = IntelligenceGenome()
        
        # Inherit from more intelligent parent with higher probability
        if parent1.intelligence_score > parent2.intelligence_score:
            smart_parent, other_parent = parent1, parent2
            smart_prob = 0.7
        else:
            smart_parent, other_parent = parent2, parent1
            smart_prob = 0.7
        
        # Model selection (prefer larger models from smarter parent)
        if random.random() < smart_prob:
            child.model_name = smart_parent.model_name
            child.model_size = smart_parent.model_size
        else:
            child.model_name = other_parent.model_name
            child.model_size = other_parent.model_size
        
        # Parameter inheritance
        for param in ['temperature', 'top_k', 'top_p', 'repetition_penalty', 'length_penalty',
                     'diversity_penalty', 'max_response_length', 'min_response_length',
                     'num_beams', 'context_window', 'reasoning_depth']:
            if random.random() < smart_prob:
                setattr(child, param, getattr(smart_parent, param))
            else:
                setattr(child, param, getattr(other_parent, param))
        
        # Boolean parameters
        child.do_sample = smart_parent.do_sample if random.random() < smart_prob else other_parent.do_sample
        
        return child
    
    def intelligent_mutation(self, genome: IntelligenceGenome, mutation_rate: float = 0.3) -> IntelligenceGenome:
        """Mutation that tends toward higher intelligence"""
        mutated = copy.deepcopy(genome)
        
        # Model upgrade mutation (chance to upgrade to larger model)
        if random.random() < 0.2:
            current_level = next((m["intelligence_level"] for m in self.model_hierarchy 
                                if m["name"] == mutated.model_name), 1)
            
            # Try to upgrade to next level
            next_level_models = [m for m in self.model_hierarchy 
                               if m["intelligence_level"] == current_level + 1]
            if next_level_models:
                new_model = random.choice(next_level_models)
                mutated.model_name = new_model["name"]
                mutated.model_size = new_model["size"]
                print(f"Intelligence upgrade: {genome.model_name} -> {mutated.model_name}")
        
        # Parameter mutations (tend toward better values)
        if random.random() < mutation_rate:
            # Bias toward lower temperature for more focused responses
            mutated.temperature = max(0.1, min(1.5, mutated.temperature + random.gauss(0, 0.1)))
        
        if random.random() < mutation_rate:
            # Bias toward higher context window
            mutated.context_window = random.choice([512, 768, 1024])
        
        if random.random() < mutation_rate:
            # Bias toward deeper reasoning
            mutated.reasoning_depth = min(5, mutated.reasoning_depth + random.choice([-1, 0, 1, 1]))
        
        if random.random() < mutation_rate:
            # Bias toward beam search for quality
            mutated.num_beams = random.choice([1, 2, 3, 4])
        
        # Other parameter mutations
        if random.random() < mutation_rate:
            mutated.top_k = max(1, min(100, mutated.top_k + random.randint(-10, 10)))
        
        if random.random() < mutation_rate:
            mutated.top_p = max(0.1, min(1.0, mutated.top_p + random.gauss(0, 0.05)))
        
        return mutated
    
    def get_smartest_ai(self) -> IntelligenceGenome:
        """Get the most intelligent AI from population"""
        return max(self.population, key=lambda x: x.intelligence_score)

def create_intelligence_test_questions() -> List[Dict]:
    """Create test questions to evaluate AI intelligence"""
    return [
        {"question": "Explain the concept of artificial intelligence and its potential impact on society.", 
         "category": "technology", "difficulty": 4},
        {"question": "What is the difference between correlation and causation?", 
         "category": "science", "difficulty": 3},
        {"question": "How would you solve a complex problem step by step?", 
         "category": "general", "difficulty": 3},
        {"question": "What are the ethical implications of autonomous vehicles?", 
         "category": "philosophy", "difficulty": 4},
        {"question": "Describe how machine learning algorithms work.", 
         "category": "technology", "difficulty": 4},
        {"question": "What is consciousness and how might it relate to AI?", 
         "category": "philosophy", "difficulty": 5},
        {"question": "Explain the scientific method and why it's important.", 
         "category": "science", "difficulty": 3},
        {"question": "How do you think technology will change in the next 10 years?", 
         "category": "technology", "difficulty": 3},
        {"question": "What makes a good argument or reasoning process?", 
         "category": "philosophy", "difficulty": 4},
        {"question": "How would you explain quantum physics to someone?", 
         "category": "science", "difficulty": 5}
    ]

def main():
    """Main intelligence evolution experiment"""
    print("🧠 AI INTELLIGENCE EVOLUTION EXPERIMENT 🧠")
    print("This will evolve AI to become smarter through:")
    print("- Larger, more powerful models")
    print("- Advanced reasoning parameters")
    print("- Intelligence-focused fitness evaluation")
    print()
    
    # Create intelligence evolution system
    evolution = IntelligenceEvolution(population_size=6)  # Smaller population for larger models
    evolution.create_initial_population()
    
    # Create intelligence test questions
    test_questions = create_intelligence_test_questions()
    
    # Run intelligence evolution
    smartest_ai = evolution.evolve_intelligence(test_questions, num_generations=4)
    
    print(f"\n🎓 INTELLIGENCE EVOLUTION COMPLETE!")
    print(f"Smartest AI: {smartest_ai.id}")
    print(f"Model: {smartest_ai.model_name} ({smartest_ai.model_size})")
    print(f"Intelligence Score: {smartest_ai.intelligence_score:.3f}")
    print(f"Complexity Score: {smartest_ai.complexity_score:.3f}")
    print(f"Coherence Score: {smartest_ai.coherence_score:.3f}")
    print(f"Knowledge Score: {smartest_ai.knowledge_score:.3f}")
    print(f"Overall Fitness: {smartest_ai.fitness:.3f}")
    
    # Save the smartest AI
    smart_ai_data = asdict(smartest_ai)
    with open('smartest_ai_genome.json', 'w') as f:
        json.dump(smart_ai_data, f, indent=2)
    
    print("Smartest AI genome saved to 'smartest_ai_genome.json'")
    
    # Test the smartest AI
    print(f"\n🧠 Testing the smartest AI...")
    evolution_engine = evolution
    
    test_question = "Explain how artificial intelligence works and its future potential."
    response = evolution_engine.generate_intelligent_response(smartest_ai, test_question)
    
    print(f"Question: {test_question}")
    print(f"Smart AI Response: {response}")

if __name__ == "__main__":
    main()
