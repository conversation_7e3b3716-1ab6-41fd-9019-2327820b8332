from transformers import AutoModelForCausalLM, AutoTokenizer, Conversation, ConversationalPipeline
import torch

def create_simple_chatbot():
    """Create a simple chatbot using ConversationalPipeline"""
    model_name = "microsoft/DialoGPT-small"
    
    # Create conversational pipeline
    chatbot = ConversationalPipeline(
        model=AutoModelForCausalLM.from_pretrained(model_name),
        tokenizer=AutoTokenizer.from_pretrained(model_name),
        device=0 if torch.cuda.is_available() else -1
    )
    
    return chatbot

def create_manual_chatbot():
    """Create a chatbot with manual conversation handling"""
    model_name = "microsoft/DialoGPT-small"
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(model_name)
    
    # Set pad token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    return model, tokenizer

def chat_with_pipeline():
    """Chat using the ConversationalPipeline approach"""
    print("=== Pipeline-based Chatbot ===")
    print("Talk to the bot! (type 'quit' to exit)")
    
    chatbot = create_simple_chatbot()
    conversation = Conversation()
    
    while True:
        try:
            user_input = input("You: ")
            if user_input.lower() == "quit":
                break
            
            conversation.add_user_input(user_input)
            conversation = chatbot(conversation)
            
            print("Bot:", conversation.generated_responses[-1])
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"An error occurred: {e}")
            continue

def chat_manually():
    """Chat using manual conversation handling"""
    print("=== Manual Chatbot ===")
    print("Talk to the bot! (type 'quit' to exit)")
    
    model, tokenizer = create_manual_chatbot()
    chat_history_ids = None
    
    for step in range(20):  # Allow more turns
        try:
            user_input = input("You: ")
            if user_input.lower() == "quit":
                break
            
            # Encode the new user input, add the eos_token and return a tensor in Pytorch
            new_user_input_ids = tokenizer.encode(user_input + tokenizer.eos_token, return_tensors='pt')
            
            # Append the new user input tokens to the chat history
            bot_input_ids = torch.cat([chat_history_ids, new_user_input_ids], dim=-1) if chat_history_ids is not None else new_user_input_ids
            
            # Generate a response while limiting the total chat history to 1000 tokens
            with torch.no_grad():
                chat_history_ids = model.generate(
                    bot_input_ids, 
                    max_length=min(1000, bot_input_ids.shape[-1] + 100),
                    num_beams=1,
                    do_sample=True,
                    top_p=0.95,
                    top_k=50,
                    temperature=0.75,
                    pad_token_id=tokenizer.eos_token_id,
                    repetition_penalty=1.1,
                    no_repeat_ngram_size=3
                )
            
            # Pretty print last ouput tokens from bot
            bot_response = tokenizer.decode(chat_history_ids[:, bot_input_ids.shape[-1]:][0], skip_special_tokens=True)
            bot_response = bot_response.strip()
            
            if not bot_response:
                bot_response = "I'm not sure what to say."
            
            print("Bot:", bot_response)
            
            # Trim chat history if it gets too long
            if chat_history_ids.shape[-1] > 800:
                chat_history_ids = chat_history_ids[:, -600:]
                
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"An error occurred: {e}")
            continue

if __name__ == "__main__":
    print("Choose chatbot version:")
    print("1. Pipeline-based (recommended)")
    print("2. Manual implementation")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        chat_with_pipeline()
    elif choice == "2":
        chat_manually()
    else:
        print("Invalid choice. Using pipeline-based chatbot.")
        chat_with_pipeline()
