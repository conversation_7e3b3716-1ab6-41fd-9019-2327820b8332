from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import json
from dataclasses import dataclass, asdict

@dataclass
class EvolvedGenome:
    """The evolved genome from your evolution experiment"""
    temperature: float = 1.0664064131832431
    top_k: int = 55
    top_p: float = 0.9257422967302524
    repetition_penalty: float = 1.0030921193878959
    max_response_length: int = 37
    fitness: float = 0.7318211598746082
    generation: int = 5
    id: str = "gen5_elite0"

class EvolvedChatbot:
    """Your evolved chatbot - the final result of the evolution process"""
    
    def __init__(self, model_name: str = "gpt2"):
        self.genome = EvolvedGenome()
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        
        # Setup device (CUDA if available)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Evolved Chatbot using device: {self.device}")
        
        if self.device.type == "cuda":
            print(f"GPU: {torch.cuda.get_device_name()}")
        
        self.load_model()
        
    def load_model(self):
        """Load the model with optimizations"""
        print(f"Loading evolved model: {self.model_name}")
        
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        
        # Use optimized loading for GPU
        if self.device.type == "cuda":
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                dtype=torch.float16,  # Half precision for speed
                device_map="auto"
            )
        else:
            self.model = AutoModelForCausalLM.from_pretrained(self.model_name)
            self.model = self.model.to(self.device)
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        print("Evolved model loaded!")
        print(f"Evolved parameters: {asdict(self.genome)}")
    
    def generate_response(self, user_input: str) -> str:
        """Generate response using the evolved parameters"""
        prompt = f"Human: {user_input}\nAssistant:"
        
        # Encode input
        input_ids = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)
        
        # Generate with evolved parameters
        with torch.no_grad():
            output = self.model.generate(
                input_ids,
                max_length=input_ids.shape[-1] + self.genome.max_response_length,
                do_sample=True,
                temperature=self.genome.temperature,
                top_k=self.genome.top_k,
                top_p=self.genome.top_p,
                repetition_penalty=self.genome.repetition_penalty,
                pad_token_id=self.tokenizer.eos_token_id,
                attention_mask=torch.ones(input_ids.shape, device=self.device)
            )
        
        # Extract response
        response = self.tokenizer.decode(output[0], skip_special_tokens=True)
        
        if "Assistant:" in response:
            bot_response = response.split("Assistant:")[-1].strip()
            if "Human:" in bot_response:
                bot_response = bot_response.split("Human:")[0].strip()
        else:
            bot_response = "I'm not sure how to respond."
        
        return bot_response.strip()
    
    def chat(self):
        """Start an interactive chat session with the evolved chatbot"""
        print("\n" + "="*60)
        print("🧬 EVOLVED CHATBOT - GENERATION 5 ELITE 🧬")
        print("="*60)
        print(f"Fitness Score: {self.genome.fitness:.3f}")
        print(f"Evolution ID: {self.genome.id}")
        print("Type 'quit' to exit, 'info' for genome details")
        print("="*60)
        
        conversation_count = 0
        
        while True:
            try:
                user_input = input("\nYou: ")
                
                if user_input.lower() == "quit":
                    print("Goodbye! Thanks for chatting with the evolved bot! 🤖")
                    break
                elif user_input.lower() == "info":
                    self.show_genome_info()
                    continue
                elif user_input.lower() == "save":
                    self.save_genome()
                    continue
                
                response = self.generate_response(user_input)
                print(f"Bot: {response}")
                
                conversation_count += 1
                
            except KeyboardInterrupt:
                print("\n\nGoodbye! Thanks for chatting with the evolved bot! 🤖")
                break
            except Exception as e:
                print(f"Error: {e}")
                print("Let's try again...")
    
    def show_genome_info(self):
        """Display detailed information about the evolved genome"""
        print("\n" + "="*50)
        print("🧬 EVOLVED GENOME DETAILS")
        print("="*50)
        print(f"Generation: {self.genome.generation}")
        print(f"ID: {self.genome.id}")
        print(f"Fitness Score: {self.genome.fitness:.6f}")
        print("\nEvolved Parameters:")
        print(f"  Temperature: {self.genome.temperature:.6f} (creativity/randomness)")
        print(f"  Top-K: {self.genome.top_k} (vocabulary selection)")
        print(f"  Top-P: {self.genome.top_p:.6f} (nucleus sampling)")
        print(f"  Repetition Penalty: {self.genome.repetition_penalty:.6f}")
        print(f"  Max Response Length: {self.genome.max_response_length} tokens")
        print("="*50)
    
    def save_genome(self, filename: str = "evolved_genome.json"):
        """Save the evolved genome to a file"""
        genome_data = asdict(self.genome)
        
        with open(filename, 'w') as f:
            json.dump(genome_data, f, indent=2)
        
        print(f"Evolved genome saved to {filename}")
    
    @classmethod
    def load_from_file(cls, filename: str, model_name: str = "gpt2"):
        """Load an evolved chatbot from a saved genome file"""
        with open(filename, 'r') as f:
            genome_data = json.load(f)
        
        chatbot = cls(model_name)
        chatbot.genome = EvolvedGenome(**genome_data)
        
        print(f"Loaded evolved genome from {filename}")
        return chatbot

def quick_test():
    """Quick test of the evolved chatbot"""
    print("🧬 Quick Test of Evolved Chatbot")
    
    bot = EvolvedChatbot()
    
    test_inputs = [
        "Hello, how are you?",
        "What do you think about technology?",
        "Tell me something interesting",
        "What's your favorite thing?",
        "How do you feel about the future?"
    ]
    
    print("\nTesting evolved responses:")
    print("-" * 40)
    
    for test_input in test_inputs:
        response = bot.generate_response(test_input)
        print(f"Human: {test_input}")
        print(f"Bot: {response}")
        print()

def main():
    """Main function - start the evolved chatbot"""
    print("🧬 EVOLVED CHATBOT - FINAL VERSION 🧬")
    print("This is your evolved chatbot from the genetic algorithm!")
    print("Choose an option:")
    print("1. Chat with the evolved bot")
    print("2. Quick test")
    print("3. Show genome info only")
    
    choice = input("\nEnter choice (1, 2, or 3): ").strip()
    
    if choice == "1":
        bot = EvolvedChatbot()
        bot.chat()
    elif choice == "2":
        quick_test()
    elif choice == "3":
        bot = EvolvedChatbot()
        bot.show_genome_info()
    else:
        print("Invalid choice. Starting chat...")
        bot = EvolvedChatbot()
        bot.chat()

if __name__ == "__main__":
    main()
