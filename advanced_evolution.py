from cuda_evolution import CUDAEvolutionEngine, ChatbotGenome
import copy
import random
import numpy as np
import json

class AdvancedEvolution(CUDAEvolutionEngine):
    """Advanced evolution strategies for AI improvement"""
    
    def __init__(self, population_size: int = 15, model_name: str = "gpt2"):
        super().__init__(population_size, model_name)
        self.evolution_history = []
        
    def adaptive_mutation(self, genome: ChatbotGenome, generation: int, max_generations: int) -> ChatbotGenome:
        """Adaptive mutation rate that changes over generations"""
        # Start with high mutation, decrease over time
        base_rate = 0.3
        adaptive_rate = base_rate * (1.0 - generation / max_generations)
        adaptive_rate = max(0.05, adaptive_rate)  # Minimum mutation rate
        
        print(f"Generation {generation}: Using mutation rate {adaptive_rate:.3f}")
        
        return self.mutate(genome, mutation_rate=adaptive_rate)
    
    def island_evolution(self, num_islands: int = 3, generations_per_island: int = 3, migration_rate: float = 0.2):
        """Island evolution - multiple isolated populations that occasionally exchange individuals"""
        print(f"🏝️ ISLAND EVOLUTION: {num_islands} islands, {generations_per_island} gens per island")
        
        # Create islands (sub-populations)
        island_size = self.population_size // num_islands
        islands = []
        
        for i in range(num_islands):
            island_pop = self.population[i * island_size:(i + 1) * island_size]
            islands.append(island_pop)
        
        test_inputs = [
            "Hello", "How are you?", "What do you think about music?",
            "Tell me a joke", "What's your opinion?", "Can you help me?",
            "What's interesting?", "How do you feel?", "What do you like?", "Tell me more"
        ]
        
        total_generations = 0
        
        for cycle in range(3):  # 3 cycles of island evolution
            print(f"\n🌊 ISLAND CYCLE {cycle + 1}")
            
            # Evolve each island separately
            for island_idx, island in enumerate(islands):
                print(f"  Evolving Island {island_idx + 1}...")
                
                # Temporarily set population to this island
                old_pop = self.population
                self.population = island
                
                # Evolve for a few generations
                for gen in range(generations_per_island):
                    self.evolve_generation(test_inputs)
                    total_generations += 1
                
                # Update island
                islands[island_idx] = self.population
                
                # Restore full population
                self.population = old_pop
            
            # Migration between islands
            if cycle < 2:  # Don't migrate on last cycle
                print(f"  🚢 Migration between islands...")
                self.migrate_between_islands(islands, migration_rate)
        
        # Combine all islands and select best
        all_individuals = []
        for island in islands:
            all_individuals.extend(island)
        
        # Sort by fitness and take the best
        all_individuals.sort(key=lambda x: x.fitness, reverse=True)
        self.population = all_individuals[:self.population_size]
        
        return self.get_best_genome()
    
    def migrate_between_islands(self, islands, migration_rate: float):
        """Exchange individuals between islands"""
        for i, island in enumerate(islands):
            num_migrants = int(len(island) * migration_rate)
            
            if num_migrants > 0:
                # Select best individuals to migrate
                island.sort(key=lambda x: x.fitness, reverse=True)
                migrants = island[:num_migrants]
                
                # Send to random other islands
                for migrant in migrants:
                    target_island = random.choice([j for j in range(len(islands)) if j != i])
                    
                    # Replace worst individual in target island
                    islands[target_island].sort(key=lambda x: x.fitness, reverse=True)
                    islands[target_island][-1] = copy.deepcopy(migrant)
                    islands[target_island][-1].id = f"migrant_to_island{target_island}"
    
    def differential_evolution(self, num_generations: int = 5):
        """Differential Evolution strategy"""
        print("🔄 DIFFERENTIAL EVOLUTION")
        
        test_inputs = [
            "Hello", "How are you?", "What do you think about music?",
            "Tell me a joke", "What's your opinion?", "Can you help me?",
            "What's interesting?", "How do you feel?", "What do you like?", "Tell me more"
        ]
        
        for generation in range(num_generations):
            print(f"\n--- Differential Evolution Generation {generation + 1} ---")
            
            # Evaluate current population
            self.evaluate_fitness_batch(test_inputs)
            
            new_population = []
            
            for i, target in enumerate(self.population):
                # Select three random individuals (different from target)
                candidates = [x for j, x in enumerate(self.population) if j != i]
                a, b, c = random.sample(candidates, 3)
                
                # Create mutant vector: a + F * (b - c)
                F = 0.5  # Differential weight
                mutant = self.create_differential_mutant(a, b, c, F)
                
                # Crossover
                CR = 0.7  # Crossover probability
                trial = self.differential_crossover(target, mutant, CR)
                
                # Selection
                trial_fitness = self.evaluate_individual_fitness(trial, test_inputs)
                
                if trial_fitness > target.fitness:
                    trial.fitness = trial_fitness
                    trial.generation = generation + 1
                    trial.id = f"gen{generation + 1}_de{i}"
                    new_population.append(trial)
                else:
                    new_population.append(target)
            
            self.population = new_population
            self.generation = generation + 1
            
            # Show progress
            fitnesses = [x.fitness for x in self.population]
            print(f"Generation {generation + 1} - Best: {max(fitnesses):.3f}, Avg: {np.mean(fitnesses):.3f}")
        
        return self.get_best_genome()
    
    def create_differential_mutant(self, a: ChatbotGenome, b: ChatbotGenome, c: ChatbotGenome, F: float) -> ChatbotGenome:
        """Create mutant vector for differential evolution"""
        mutant = ChatbotGenome()
        
        # a + F * (b - c) for each parameter
        mutant.temperature = max(0.1, min(2.0, a.temperature + F * (b.temperature - c.temperature)))
        mutant.top_k = max(1, min(100, int(a.top_k + F * (b.top_k - c.top_k))))
        mutant.top_p = max(0.1, min(1.0, a.top_p + F * (b.top_p - c.top_p)))
        mutant.repetition_penalty = max(1.0, min(2.0, a.repetition_penalty + F * (b.repetition_penalty - c.repetition_penalty)))
        mutant.max_response_length = max(10, min(100, int(a.max_response_length + F * (b.max_response_length - c.max_response_length))))
        
        return mutant
    
    def differential_crossover(self, target: ChatbotGenome, mutant: ChatbotGenome, CR: float) -> ChatbotGenome:
        """Crossover for differential evolution"""
        trial = copy.deepcopy(target)
        
        # Ensure at least one parameter comes from mutant
        forced_param = random.choice(['temperature', 'top_k', 'top_p', 'repetition_penalty', 'max_response_length'])
        
        for param in ['temperature', 'top_k', 'top_p', 'repetition_penalty', 'max_response_length']:
            if random.random() < CR or param == forced_param:
                setattr(trial, param, getattr(mutant, param))
        
        return trial
    
    def evaluate_individual_fitness(self, genome: ChatbotGenome, test_inputs: list) -> float:
        """Evaluate fitness for a single individual"""
        fitness_scores = []
        
        for test_input in test_inputs:
            try:
                response = self.generate_response(genome, test_input)
                fitness = self.calculate_fitness(response)
                fitness_scores.append(fitness)
            except:
                fitness_scores.append(0.0)
        
        return np.mean(fitness_scores) if fitness_scores else 0.0
    
    def particle_swarm_optimization(self, num_generations: int = 5):
        """Particle Swarm Optimization for parameter tuning"""
        print("🐝 PARTICLE SWARM OPTIMIZATION")
        
        # Initialize particles with velocities
        particles = []
        for genome in self.population:
            particle = {
                'position': genome,
                'velocity': self.create_zero_velocity(),
                'best_position': copy.deepcopy(genome),
                'best_fitness': 0.0
            }
            particles.append(particle)
        
        global_best = copy.deepcopy(self.population[0])
        global_best_fitness = 0.0
        
        test_inputs = [
            "Hello", "How are you?", "What do you think about music?",
            "Tell me a joke", "What's your opinion?", "Can you help me?",
            "What's interesting?", "How do you feel?", "What do you like?", "Tell me more"
        ]
        
        for generation in range(num_generations):
            print(f"\n--- PSO Generation {generation + 1} ---")
            
            for i, particle in enumerate(particles):
                # Evaluate fitness
                fitness = self.evaluate_individual_fitness(particle['position'], test_inputs)
                particle['position'].fitness = fitness
                
                # Update personal best
                if fitness > particle['best_fitness']:
                    particle['best_fitness'] = fitness
                    particle['best_position'] = copy.deepcopy(particle['position'])
                
                # Update global best
                if fitness > global_best_fitness:
                    global_best_fitness = fitness
                    global_best = copy.deepcopy(particle['position'])
                
                # Update velocity and position
                self.update_particle_velocity(particle, global_best)
                self.update_particle_position(particle)
            
            # Show progress
            fitnesses = [p['position'].fitness for p in particles]
            print(f"Generation {generation + 1} - Best: {max(fitnesses):.3f}, Avg: {np.mean(fitnesses):.3f}")
        
        # Update population with final positions
        self.population = [p['position'] for p in particles]
        
        return global_best
    
    def create_zero_velocity(self) -> dict:
        """Create zero velocity vector"""
        return {
            'temperature': 0.0,
            'top_k': 0,
            'top_p': 0.0,
            'repetition_penalty': 0.0,
            'max_response_length': 0
        }
    
    def update_particle_velocity(self, particle: dict, global_best: ChatbotGenome):
        """Update particle velocity"""
        w = 0.5  # Inertia weight
        c1 = 1.5  # Cognitive parameter
        c2 = 1.5  # Social parameter
        
        velocity = particle['velocity']
        position = particle['position']
        personal_best = particle['best_position']
        
        # Update each dimension
        velocity['temperature'] = (w * velocity['temperature'] + 
                                 c1 * random.random() * (personal_best.temperature - position.temperature) +
                                 c2 * random.random() * (global_best.temperature - position.temperature))
        
        velocity['top_k'] = (w * velocity['top_k'] + 
                           c1 * random.random() * (personal_best.top_k - position.top_k) +
                           c2 * random.random() * (global_best.top_k - position.top_k))
        
        velocity['top_p'] = (w * velocity['top_p'] + 
                           c1 * random.random() * (personal_best.top_p - position.top_p) +
                           c2 * random.random() * (global_best.top_p - position.top_p))
        
        velocity['repetition_penalty'] = (w * velocity['repetition_penalty'] + 
                                        c1 * random.random() * (personal_best.repetition_penalty - position.repetition_penalty) +
                                        c2 * random.random() * (global_best.repetition_penalty - position.repetition_penalty))
        
        velocity['max_response_length'] = (w * velocity['max_response_length'] + 
                                         c1 * random.random() * (personal_best.max_response_length - position.max_response_length) +
                                         c2 * random.random() * (global_best.max_response_length - position.max_response_length))
    
    def update_particle_position(self, particle: dict):
        """Update particle position"""
        velocity = particle['velocity']
        position = particle['position']
        
        # Update position with velocity, applying constraints
        position.temperature = max(0.1, min(2.0, position.temperature + velocity['temperature']))
        position.top_k = max(1, min(100, int(position.top_k + velocity['top_k'])))
        position.top_p = max(0.1, min(1.0, position.top_p + velocity['top_p']))
        position.repetition_penalty = max(1.0, min(2.0, position.repetition_penalty + velocity['repetition_penalty']))
        position.max_response_length = max(10, min(100, int(position.max_response_length + velocity['max_response_length'])))

def main():
    """Main menu for advanced evolution strategies"""
    print("🚀 ADVANCED EVOLUTION STRATEGIES 🚀")
    print("Choose an advanced evolution method:")
    print()
    print("1. Island Evolution (multiple isolated populations)")
    print("2. Differential Evolution (vector-based optimization)")
    print("3. Particle Swarm Optimization (swarm intelligence)")
    print("4. Adaptive Mutation Evolution (changing mutation rates)")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    # Load your best evolved genome
    best_evolved_params = {
        'temperature': 1.0664064131832431,
        'top_k': 55,
        'top_p': 0.9257422967302524,
        'repetition_penalty': 1.0030921193878959,
        'max_response_length': 37,
        'fitness': 0.7318211598746082,
        'generation': 5,
        'id': 'gen5_elite0'
    }
    
    # Create evolution engine
    evolution = AdvancedEvolution(population_size=15, model_name="gpt2")
    
    # Create population based on best genome
    elite_genome = ChatbotGenome(**best_evolved_params)
    evolution.population = [elite_genome]
    
    for i in range(1, evolution.population_size):
        variant = copy.deepcopy(elite_genome)
        variant = evolution.mutate(variant, mutation_rate=0.2)
        variant.id = f"variant_{i}"
        variant.fitness = 0.0
        evolution.population.append(variant)
    
    print(f"Starting from fitness: {best_evolved_params['fitness']:.3f}")
    
    if choice == "1":
        improved_genome = evolution.island_evolution()
    elif choice == "2":
        improved_genome = evolution.differential_evolution()
    elif choice == "3":
        improved_genome = evolution.particle_swarm_optimization()
    elif choice == "4":
        # Adaptive mutation with regular evolution
        test_inputs = [
            "Hello", "How are you?", "What do you think about music?",
            "Tell me a joke", "What's your opinion?", "Can you help me?",
            "What's interesting?", "How do you feel?", "What do you like?", "Tell me more"
        ]
        
        for gen in range(5):
            evolution.evolve_generation(test_inputs)
            # Use adaptive mutation in the evolution process
            for i, genome in enumerate(evolution.population):
                if i > evolution.population_size // 3:  # Don't mutate elites
                    evolution.population[i] = evolution.adaptive_mutation(genome, gen, 5)
        
        improved_genome = evolution.get_best_genome()
    else:
        print("Invalid choice. Using Island Evolution...")
        improved_genome = evolution.island_evolution()
    
    print(f"\n🎉 ADVANCED EVOLUTION COMPLETE!")
    print(f"Original fitness: {best_evolved_params['fitness']:.3f}")
    print(f"New fitness: {improved_genome.fitness:.3f}")
    improvement = ((improved_genome.fitness - best_evolved_params['fitness']) / best_evolved_params['fitness'] * 100)
    print(f"Improvement: {improvement:+.1f}%")
    
    # Save improved genome
    improved_params = {
        'temperature': improved_genome.temperature,
        'top_k': improved_genome.top_k,
        'top_p': improved_genome.top_p,
        'repetition_penalty': improved_genome.repetition_penalty,
        'max_response_length': improved_genome.max_response_length,
        'fitness': improved_genome.fitness,
        'generation': improved_genome.generation,
        'id': improved_genome.id
    }
    
    with open('advanced_evolved_genome.json', 'w') as f:
        json.dump(improved_params, f, indent=2)
    
    print("Advanced evolved genome saved to 'advanced_evolved_genome.json'")

if __name__ == "__main__":
    main()
