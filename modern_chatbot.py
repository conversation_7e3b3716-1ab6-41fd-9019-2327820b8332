from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

def create_modern_chatbot():
    """Use a more modern and reliable conversational model"""
    # Try DialoGPT-medium (more stable than small) or fall back to GPT-2
    models_to_try = [
        "microsoft/DialoGPT-medium",  # Better than small version
        "gpt2-medium",                # Larger GPT-2
        "gpt2"                        # Fallback
    ]
    
    for model_name in models_to_try:
        try:
            print(f"Trying to load model: {model_name}")
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForCausalLM.from_pretrained(model_name)
            
            # Set pad token
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            print(f"Successfully loaded: {model_name}")
            return model, tokenizer, model_name
            
        except Exception as e:
            print(f"Failed to load {model_name}: {e}")
            continue
    
    raise Exception("Could not load any model")

def chat_with_modern_model():
    """Chat using a more reliable model with better conversation handling"""
    print("=== Modern Chatbot ===")
    
    try:
        model, tokenizer, model_name = create_modern_chatbot()
    except Exception as e:
        print(f"Error loading model: {e}")
        return
    
    print("Talk to the bot! (type 'quit' to exit)")
    
    if "DialoGPT" in model_name:
        # Use DialoGPT-specific conversation handling
        chat_history_ids = None
        
        for step in range(15):
            try:
                user_input = input("You: ")
                if user_input.lower() == "quit":
                    break
                
                # Encode user input
                new_user_input_ids = tokenizer.encode(user_input + tokenizer.eos_token, return_tensors='pt')
                
                # Append to chat history
                bot_input_ids = torch.cat([chat_history_ids, new_user_input_ids], dim=-1) if chat_history_ids is not None else new_user_input_ids
                
                # Limit input length to prevent memory issues
                if bot_input_ids.shape[-1] > 512:
                    bot_input_ids = bot_input_ids[:, -400:]  # Keep last 400 tokens
                
                # Generate response
                with torch.no_grad():
                    chat_history_ids = model.generate(
                        bot_input_ids,
                        max_length=bot_input_ids.shape[-1] + 100,
                        num_beams=1,
                        do_sample=True,
                        top_p=0.9,
                        top_k=50,
                        temperature=0.8,
                        pad_token_id=tokenizer.eos_token_id,
                        repetition_penalty=1.2,
                        no_repeat_ngram_size=3,
                        early_stopping=True
                    )
                
                # Extract bot response
                bot_response = tokenizer.decode(chat_history_ids[:, bot_input_ids.shape[-1]:][0], skip_special_tokens=True)
                bot_response = bot_response.strip()
                
                if not bot_response:
                    bot_response = "I'm not sure how to respond."
                
                # Clean up response - remove any weird artifacts
                bot_response = ' '.join(bot_response.split())  # Normalize whitespace
                
                print("Bot:", bot_response)
                
            except KeyboardInterrupt:
                print("\nGoodbye!")
                break
            except Exception as e:
                print(f"Error during conversation: {e}")
                continue
    
    else:
        # Use GPT-2 style conversation handling
        conversation = ""
        
        for step in range(15):
            try:
                user_input = input("You: ")
                if user_input.lower() == "quit":
                    break
                
                # Format as a conversation
                if conversation:
                    prompt = f"{conversation}\nUser: {user_input}\nBot:"
                else:
                    prompt = f"User: {user_input}\nBot:"
                
                # Encode
                input_ids = tokenizer.encode(prompt, return_tensors="pt")
                
                # Limit length
                if input_ids.shape[-1] > 400:
                    input_ids = input_ids[:, -300:]
                
                # Generate
                with torch.no_grad():
                    output = model.generate(
                        input_ids,
                        max_length=input_ids.shape[-1] + 50,
                        do_sample=True,
                        top_k=50,
                        top_p=0.9,
                        temperature=0.7,
                        repetition_penalty=1.1,
                        pad_token_id=tokenizer.eos_token_id
                    )
                
                # Extract response
                response = tokenizer.decode(output[0], skip_special_tokens=True)
                
                if "Bot:" in response:
                    bot_response = response.split("Bot:")[-1].strip()
                    # Stop at next "User:" if present
                    if "User:" in bot_response:
                        bot_response = bot_response.split("User:")[0].strip()
                else:
                    bot_response = "I'm not sure about that."
                
                # Clean and validate
                bot_response = bot_response.strip()
                if not bot_response:
                    bot_response = "Could you rephrase that?"
                
                print("Bot:", bot_response)
                
                # Update conversation
                conversation = f"{conversation}\nUser: {user_input}\nBot: {bot_response}"
                
                # Trim if too long
                if len(conversation) > 800:
                    lines = conversation.split('\n')
                    conversation = '\n'.join(lines[-8:])
                
            except KeyboardInterrupt:
                print("\nGoodbye!")
                break
            except Exception as e:
                print(f"Error during conversation: {e}")
                continue

if __name__ == "__main__":
    chat_with_modern_model()
