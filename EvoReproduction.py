from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

# Load small conversational model
model_name = "microsoft/DialoGPT-small"  # ~110M params
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForCausalLM.from_pretrained(model_name)

# Set pad token if not already set
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

# Chat loop
print("Talk to the bot! (type 'quit' to exit)")
chat_history_ids = None

for step in range(10):  # Increased to 10 turns max
    try:
        user_input = input("You: ")
        if user_input.lower() == "quit":
            break

        # Encode user input with proper formatting
        new_user_input_ids = tokenizer.encode(user_input + tokenizer.eos_token, return_tensors="pt")

        # Concatenate with chat history if it exists
        if chat_history_ids is not None:
            bot_input_ids = torch.cat([chat_history_ids, new_user_input_ids], dim=-1)
        else:
            bot_input_ids = new_user_input_ids

        # Create attention mask
        attention_mask = torch.ones(bot_input_ids.shape, dtype=torch.long)

        # Generate response with improved parameters
        with torch.no_grad():
            chat_history_ids = model.generate(
                bot_input_ids,
                attention_mask=attention_mask,
                max_length=bot_input_ids.shape[-1] + 50,  # Limit response length
                min_length=bot_input_ids.shape[-1] + 1,   # Ensure some response
                pad_token_id=tokenizer.eos_token_id,
                do_sample=True,
                top_k=50,
                top_p=0.95,
                temperature=0.7,
                repetition_penalty=1.1,
                no_repeat_ngram_size=3
            )

        # Extract only the bot's response (everything after the input)
        bot_response = chat_history_ids[:, bot_input_ids.shape[-1]:]
        bot_reply = tokenizer.decode(bot_response[0], skip_special_tokens=True)

        # Clean up the response
        bot_reply = bot_reply.strip()
        if not bot_reply:
            bot_reply = "I'm not sure how to respond to that."

        print("Bot:", bot_reply)

        # Keep conversation history manageable (limit to last 500 tokens)
        if chat_history_ids.shape[-1] > 500:
            chat_history_ids = chat_history_ids[:, -400:]  # Keep last 400 tokens

    except KeyboardInterrupt:
        print("\nGoodbye!")
        break
    except Exception as e:
        print(f"An error occurred: {e}")
        print("Let's try again...")
        continue
