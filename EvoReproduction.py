#!/usr/bin/env python3
"""
🧬 ULTIMATE AI EVOLUTION SYSTEM 🧬
The one and only script for evolving AI chatbots!

Features:
- Basic Evolution: Evolve generation parameters
- Smart Evolution: Evolve for intelligence
- Model Evolution: Evolve to larger models
- Parameter Growth: Actually grow model size
- Interactive Chat: Test your evolved AI
- Save/Load: Persist your best genomes

Author: Your Evolution Lab
"""

from transformers import AutoModelForCausalLM, AutoTokenizer, GPT2Config
import torch
import random
import json
import copy
import time
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional
import numpy as np

@dataclass
class UltimateGenome:
    """Ultimate genome that can evolve everything"""
    # Basic generation parameters
    temperature: float = 0.7
    top_k: int = 50
    top_p: float = 0.95
    repetition_penalty: float = 1.1
    max_response_length: int = 50

    # Advanced parameters
    num_beams: int = 1
    do_sample: bool = True
    length_penalty: float = 1.0
    min_response_length: int = 10

    # Model selection (intelligence level)
    model_name: str = "gpt2"
    intelligence_level: int = 1  # 1=small, 2=medium, 3=large

    # Architecture parameters (for model growth)
    n_layers: int = 12
    n_embd: int = 768
    n_heads: int = 12
    estimated_params: int = 124_000_000

    # Performance metrics
    fitness: float = 0.0
    intelligence_score: float = 0.0
    creativity_score: float = 0.0
    coherence_score: float = 0.0

    # Evolution metadata
    generation: int = 0
    id: str = ""
    evolution_type: str = "basic"  # basic, smart, model, growth

class UltimateAI:
    """The ultimate AI that can evolve in multiple ways"""

    def __init__(self, genome: UltimateGenome, device: Optional[str] = None):
        self.genome = genome
        self.model = None
        self.tokenizer = None
        self.conversation_history = None

        # Set device (CUDA if available)
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)

        # Model cache for efficiency
        self.model_cache = {}

    def load_model(self):
        """Load the transformer model with optimizations"""
        model_name = self.genome.model_name

        if model_name not in self.model_cache:
            print(f"Loading model: {model_name} on {self.device}")
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)

                if self.device.type == "cuda":
                    self.model = AutoModelForCausalLM.from_pretrained(
                        model_name,
                        dtype=torch.float16,  # Half precision for speed
                        device_map="auto"
                    )
                else:
                    self.model = AutoModelForCausalLM.from_pretrained(model_name)
                    self.model = self.model.to(self.device)

                if self.tokenizer.pad_token is None:
                    self.tokenizer.pad_token = self.tokenizer.eos_token

                self.model_cache[model_name] = {"model": self.model, "tokenizer": self.tokenizer}
                print(f"✅ Model loaded: {model_name}")

            except Exception as e:
                print(f"❌ Failed to load {model_name}, falling back to gpt2: {e}")
                self.genome.model_name = "gpt2"
                return self.load_model()
        else:
            cached = self.model_cache[model_name]
            self.model = cached["model"]
            self.tokenizer = cached["tokenizer"]

    def generate_response(self, user_input: str) -> str:
        """Generate response using evolved parameters"""
        if self.model is None:
            self.load_model()

        # Smart prompting based on intelligence level
        if self.genome.intelligence_level >= 2:
            prompt = f"Question: {user_input}\n\nThinking carefully about this, my response is:\n\nAnswer:"
        else:
            prompt = f"Human: {user_input}\nAssistant:"

        # Encode input
        input_ids = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)

        # Limit context length
        max_context = 512
        if input_ids.shape[-1] > max_context:
            input_ids = input_ids[:, -max_context:]

        # Advanced generation with evolved parameters
        generation_kwargs = {
            "input_ids": input_ids,
            "max_length": input_ids.shape[-1] + self.genome.max_response_length,
            "min_length": input_ids.shape[-1] + self.genome.min_response_length,
            "temperature": self.genome.temperature,
            "top_k": self.genome.top_k,
            "top_p": self.genome.top_p,
            "repetition_penalty": self.genome.repetition_penalty,
            "length_penalty": self.genome.length_penalty,
            "do_sample": self.genome.do_sample,
            "pad_token_id": self.tokenizer.eos_token_id,
            "attention_mask": torch.ones(input_ids.shape, device=self.device)
        }

        # Use beam search for higher intelligence
        if self.genome.num_beams > 1:
            generation_kwargs["num_beams"] = self.genome.num_beams
            generation_kwargs["early_stopping"] = True

        with torch.no_grad():
            output = self.model.generate(**generation_kwargs)

        # Extract response
        response = self.tokenizer.decode(output[0], skip_special_tokens=True)

        if "Answer:" in response:
            bot_response = response.split("Answer:")[-1].strip()
        elif "Assistant:" in response:
            bot_response = response.split("Assistant:")[-1].strip()
        else:
            bot_response = response[len(self.tokenizer.decode(input_ids[0], skip_special_tokens=True)):].strip()

        # Clean up
        if "Human:" in bot_response:
            bot_response = bot_response.split("Human:")[0].strip()

        return bot_response.strip() if bot_response.strip() else "I need to think more about this."

class UltimateEvolution:
    """The ultimate evolution system that can evolve everything"""

    def __init__(self, population_size: int = 6, evolution_type: str = "basic"):
        self.population_size = population_size
        self.evolution_type = evolution_type
        self.population: List[UltimateAI] = []
        self.generation = 0

        # Available models for evolution (using only GPT-2 to avoid downloads)
        self.model_hierarchy = [
            {"name": "gpt2", "level": 1, "params": 124_000_000, "description": "GPT-2 Small (124M)"},
            {"name": "gpt2", "level": 2, "params": 124_000_000, "description": "GPT-2 Optimized"},
            {"name": "gpt2", "level": 3, "params": 124_000_000, "description": "GPT-2 Advanced"},
        ]

        # Set device for CUDA acceleration
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        print(f"🧬 Ultimate Evolution System")
        print(f"Evolution Type: {evolution_type}")
        print(f"Device: {self.device}")
        if self.device.type == "cuda":
            print(f"GPU: {torch.cuda.get_device_name()}")
            print(f"CUDA Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

    def create_initial_population(self):
        """Create initial population based on evolution type"""
        print(f"Creating initial population of {self.population_size} AIs...")

        for i in range(self.population_size):
            if self.evolution_type == "basic":
                genome = self.create_basic_genome(i)
            elif self.evolution_type == "smart":
                genome = self.create_smart_genome(i)
            elif self.evolution_type == "model":
                genome = self.create_model_genome(i)
            elif self.evolution_type == "growth":
                genome = self.create_growth_genome(i)
            else:
                genome = self.create_basic_genome(i)

            ai = UltimateAI(genome, device=self.device)
            self.population.append(ai)

        print("Ultimate population created!")

    def create_basic_genome(self, index: int) -> UltimateGenome:
        """Create basic evolution genome"""
        return UltimateGenome(
            temperature=random.uniform(0.3, 1.2),
            top_k=random.randint(20, 100),
            top_p=random.uniform(0.7, 0.99),
            repetition_penalty=random.uniform(1.0, 1.5),
            max_response_length=random.randint(20, 80),
            model_name="gpt2",
            intelligence_level=1,
            generation=0,
            id=f"gen0_basic{index}",
            evolution_type="basic"
        )

    def create_smart_genome(self, index: int) -> UltimateGenome:
        """Create smart evolution genome"""
        model_choice = random.choice(self.model_hierarchy[:3])
        return UltimateGenome(
            temperature=random.uniform(0.4, 1.0),  # Lower for smarter responses
            top_k=random.randint(30, 80),
            top_p=random.uniform(0.8, 0.98),
            repetition_penalty=random.uniform(1.0, 1.3),
            max_response_length=random.randint(30, 100),
            num_beams=random.choice([1, 2, 3]),
            length_penalty=random.uniform(0.8, 1.2),
            min_response_length=random.randint(15, 40),
            model_name=model_choice["name"],
            intelligence_level=model_choice["level"],
            generation=0,
            id=f"gen0_smart{index}",
            evolution_type="smart"
        )

    def create_model_genome(self, index: int) -> UltimateGenome:
        """Create model evolution genome"""
        model_choice = random.choice(self.model_hierarchy)
        return UltimateGenome(
            temperature=random.uniform(0.5, 1.0),
            top_k=random.randint(40, 80),
            top_p=random.uniform(0.85, 0.95),
            repetition_penalty=random.uniform(1.0, 1.2),
            max_response_length=random.randint(40, 80),
            model_name=model_choice["name"],
            intelligence_level=model_choice["level"],
            estimated_params=model_choice["params"],
            generation=0,
            id=f"gen0_model{index}",
            evolution_type="model"
        )

    def create_growth_genome(self, index: int) -> UltimateGenome:
        """Create parameter growth genome"""
        scale_factor = random.uniform(1.0, 2.0)
        base_layers = 12
        base_embd = 768
        base_heads = 12

        return UltimateGenome(
            temperature=random.uniform(0.4, 0.9),
            top_k=random.randint(30, 70),
            top_p=random.uniform(0.8, 0.95),
            repetition_penalty=random.uniform(1.0, 1.3),
            max_response_length=random.randint(30, 90),
            model_name="gpt2",  # Base model
            intelligence_level=1,
            n_layers=max(4, int(base_layers * scale_factor)),
            n_embd=max(256, int(base_embd * scale_factor)),
            n_heads=max(4, int(base_heads * scale_factor)),
            estimated_params=int(124_000_000 * (scale_factor ** 2)),
            generation=0,
            id=f"gen0_growth{index}",
            evolution_type="growth"
        )

    def evaluate_fitness(self, test_inputs: List[str]):
        """Evaluate fitness based on evolution type"""
        print(f"Evaluating generation {self.generation} ({self.evolution_type} evolution)...")

        for i, ai in enumerate(self.population):
            print(f"Testing AI {i+1}/{len(self.population)}: {ai.genome.id}")

            if self.evolution_type == "smart":
                fitness = self.evaluate_intelligence(ai, test_inputs)
            elif self.evolution_type == "model":
                fitness = self.evaluate_model_performance(ai, test_inputs)
            elif self.evolution_type == "growth":
                fitness = self.evaluate_architecture_fitness(ai, test_inputs)
            else:
                fitness = self.evaluate_basic_fitness(ai, test_inputs)

            ai.genome.fitness = fitness
            print(f"  Fitness: {fitness:.3f}")

    def evaluate_basic_fitness(self, ai: UltimateAI, test_inputs: List[str]) -> float:
        """Basic fitness evaluation"""
        fitness_scores = []

        for test_input in test_inputs:
            try:
                response = ai.generate_response(test_input)
                score = self.calculate_basic_fitness(response, test_input)
                fitness_scores.append(score)
            except Exception as e:
                print(f"    Error: {e}")
                fitness_scores.append(0.0)

        return np.mean(fitness_scores) if fitness_scores else 0.0

    def calculate_basic_fitness(self, response: str, user_input: str) -> float:
        """Calculate basic fitness score"""
        if not response or len(response.strip()) == 0:
            return 0.0

        # Length fitness (prefer moderate length)
        length_score = max(0.0, 1.0 - abs(len(response) - 50) / 100.0)

        # Diversity fitness (unique words)
        words = response.lower().split()
        if len(words) > 0:
            diversity_score = len(set(words)) / len(words)
        else:
            diversity_score = 0.0

        # Coherence fitness (ends properly)
        coherence_score = 1.0 if response.strip().endswith(('.', '!', '?')) else 0.5

        return (length_score * 0.4 + diversity_score * 0.4 + coherence_score * 0.2)

    def evaluate_intelligence(self, ai: UltimateAI, test_inputs: List[str]) -> float:
        """Evaluate intelligence for smart evolution"""
        intelligence_scores = []

        for test_input in test_inputs:
            try:
                response = ai.generate_response(test_input)
                score = self.calculate_intelligence_score(response, test_input)
                intelligence_scores.append(score)
            except Exception as e:
                print(f"    Intelligence test error: {e}")
                intelligence_scores.append(0.0)

        intelligence = np.mean(intelligence_scores) if intelligence_scores else 0.0

        # Bonus for larger models
        model_bonus = ai.genome.intelligence_level * 0.1

        ai.genome.intelligence_score = intelligence
        return intelligence + model_bonus

    def calculate_intelligence_score(self, response: str, question: str) -> float:
        """Calculate intelligence score"""
        if not response or len(response.strip()) < 10:
            return 0.0

        score = 0.0

        # Length and detail
        if len(response) >= 50:
            score += 0.3
        if len(response) >= 100:
            score += 0.2

        # Smart vocabulary
        smart_words = ["analyze", "consider", "understand", "explain", "because",
                      "therefore", "however", "furthermore", "specifically"]
        smart_count = sum(1 for word in smart_words if word in response.lower())
        score += min(0.3, smart_count * 0.05)

        # Avoid dumb responses
        dumb_phrases = ["i don't know", "i'm not sure", "maybe", "i think"]
        if any(phrase in response.lower() for phrase in dumb_phrases):
            score -= 0.2

        # Coherence
        if response.strip().endswith(('.', '!', '?')):
            score += 0.2

        return min(1.0, max(0.0, score))

    def evaluate_model_performance(self, ai: UltimateAI, test_inputs: List[str]) -> float:
        """Evaluate model performance"""
        basic_fitness = self.evaluate_basic_fitness(ai, test_inputs)

        # Bonus for larger models
        model_info = next((m for m in self.model_hierarchy if m["name"] == ai.genome.model_name), None)
        if model_info:
            model_bonus = model_info["level"] * 0.15
        else:
            model_bonus = 0.0

        return basic_fitness + model_bonus

    def evaluate_architecture_fitness(self, ai: UltimateAI, test_inputs: List[str]) -> float:
        """Evaluate architecture fitness for growth evolution"""
        basic_fitness = self.evaluate_basic_fitness(ai, test_inputs)

        # Parameter efficiency bonus
        param_ratio = ai.genome.estimated_params / 124_000_000  # Relative to base GPT-2
        efficiency_bonus = min(0.3, param_ratio * 0.1)

        return basic_fitness + efficiency_bonus

    def evolve_generation(self, test_inputs: List[str]):
        """Evolve to next generation"""
        # Evaluate current population
        self.evaluate_fitness(test_inputs)

        # Sort by fitness
        self.population.sort(key=lambda x: x.genome.fitness, reverse=True)

        # Show stats
        fitnesses = [ai.genome.fitness for ai in self.population]
        print(f"Generation {self.generation} - Best: {max(fitnesses):.3f}, Avg: {np.mean(fitnesses):.3f}")

        # Create next generation
        new_population = []

        # Keep best (elitism)
        elite_count = max(1, self.population_size // 3)
        for i in range(elite_count):
            elite = copy.deepcopy(self.population[i])
            elite.genome.generation = self.generation + 1
            elite.genome.id = f"gen{self.generation + 1}_elite{i}"
            elite.model = None  # Reset model to save memory
            new_population.append(elite)

        # Create offspring
        while len(new_population) < self.population_size:
            # Select parents
            parent1 = self.tournament_selection()
            parent2 = self.tournament_selection()

            # Create child
            child_genome = self.crossover(parent1.genome, parent2.genome)
            child_genome = self.mutate(child_genome)
            child_genome.generation = self.generation + 1
            child_genome.id = f"gen{self.generation + 1}_child{len(new_population)}"
            child_genome.fitness = 0.0

            child = UltimateAI(child_genome, device=self.device)
            new_population.append(child)

        self.population = new_population
        self.generation += 1

    def tournament_selection(self, tournament_size: int = 3) -> UltimateAI:
        """Select parent using tournament selection"""
        tournament = random.sample(self.population, min(tournament_size, len(self.population)))
        return max(tournament, key=lambda x: x.genome.fitness)

    def crossover(self, parent1: UltimateGenome, parent2: UltimateGenome) -> UltimateGenome:
        """Create child through crossover"""
        child = UltimateGenome()

        # Inherit evolution type from parents
        child.evolution_type = parent1.evolution_type

        # Basic parameters
        child.temperature = parent1.temperature if random.random() < 0.5 else parent2.temperature
        child.top_k = parent1.top_k if random.random() < 0.5 else parent2.top_k
        child.top_p = parent1.top_p if random.random() < 0.5 else parent2.top_p
        child.repetition_penalty = parent1.repetition_penalty if random.random() < 0.5 else parent2.repetition_penalty
        child.max_response_length = parent1.max_response_length if random.random() < 0.5 else parent2.max_response_length

        # Advanced parameters
        child.num_beams = parent1.num_beams if random.random() < 0.5 else parent2.num_beams
        child.do_sample = parent1.do_sample if random.random() < 0.5 else parent2.do_sample
        child.length_penalty = parent1.length_penalty if random.random() < 0.5 else parent2.length_penalty
        child.min_response_length = parent1.min_response_length if random.random() < 0.5 else parent2.min_response_length

        # Model parameters
        child.model_name = parent1.model_name if random.random() < 0.5 else parent2.model_name
        child.intelligence_level = parent1.intelligence_level if random.random() < 0.5 else parent2.intelligence_level

        # Architecture parameters
        child.n_layers = parent1.n_layers if random.random() < 0.5 else parent2.n_layers
        child.n_embd = parent1.n_embd if random.random() < 0.5 else parent2.n_embd
        child.n_heads = parent1.n_heads if random.random() < 0.5 else parent2.n_heads
        child.estimated_params = parent1.estimated_params if random.random() < 0.5 else parent2.estimated_params

        return child

    def mutate(self, genome: UltimateGenome, mutation_rate: float = 0.3) -> UltimateGenome:
        """Apply mutations based on evolution type"""
        mutated = copy.deepcopy(genome)

        # Basic parameter mutations
        if random.random() < mutation_rate:
            mutated.temperature = max(0.1, min(2.0, mutated.temperature + random.gauss(0, 0.1)))

        if random.random() < mutation_rate:
            mutated.top_k = max(1, min(100, mutated.top_k + random.randint(-10, 10)))

        if random.random() < mutation_rate:
            mutated.top_p = max(0.1, min(1.0, mutated.top_p + random.gauss(0, 0.05)))

        if random.random() < mutation_rate:
            mutated.repetition_penalty = max(1.0, min(2.0, mutated.repetition_penalty + random.gauss(0, 0.05)))

        if random.random() < mutation_rate:
            mutated.max_response_length = max(10, min(150, mutated.max_response_length + random.randint(-10, 20)))

        # Evolution-specific mutations
        if genome.evolution_type == "smart":
            mutated = self.smart_mutation(mutated)
        elif genome.evolution_type == "model":
            mutated = self.model_mutation(mutated)
        elif genome.evolution_type == "growth":
            mutated = self.growth_mutation(mutated)

        return mutated

    def smart_mutation(self, genome: UltimateGenome) -> UltimateGenome:
        """Smart evolution mutations"""
        if random.random() < 0.2:
            # Upgrade intelligence level (same model, better parameters)
            current_level = genome.intelligence_level
            if current_level < 3:
                genome.intelligence_level = min(3, current_level + 1)
                print(f"    🧠 Intelligence upgrade: Level {current_level} → {genome.intelligence_level}")

        if random.random() < 0.3:
            genome.num_beams = random.choice([1, 2, 3, 4])

        if random.random() < 0.3:
            # Smarter parameter adjustments
            genome.temperature = max(0.3, min(1.0, genome.temperature * random.uniform(0.9, 1.1)))
            genome.min_response_length = max(15, min(50, genome.min_response_length + random.randint(-5, 10)))

        return genome

    def model_mutation(self, genome: UltimateGenome) -> UltimateGenome:
        """Model evolution mutations"""
        if random.random() < 0.3:
            # Try different model
            new_model = random.choice(self.model_hierarchy)
            genome.model_name = new_model["name"]
            genome.intelligence_level = new_model["level"]
            genome.estimated_params = new_model["params"]

        return genome

    def growth_mutation(self, genome: UltimateGenome) -> UltimateGenome:
        """Parameter growth mutations"""
        if random.random() < 0.2:
            # Grow architecture
            genome.n_layers = max(4, min(24, genome.n_layers + random.choice([-1, 1, 2])))
            genome.n_embd = max(256, min(2048, genome.n_embd + random.choice([-64, 64, 128])))
            genome.n_heads = max(4, min(32, genome.n_heads + random.choice([-2, 2, 4])))

            # Recalculate parameter count
            genome.estimated_params = int(genome.estimated_params * 1.2)

        return genome

    def get_best_ai(self) -> UltimateAI:
        """Get the best performing AI"""
        return max(self.population, key=lambda x: x.genome.fitness)

    def save_best_genome(self, filename: str = "best_genome.json"):
        """Save the best genome to file"""
        best_ai = self.get_best_ai()
        genome_data = asdict(best_ai.genome)

        with open(filename, 'w') as f:
            json.dump(genome_data, f, indent=2)

        print(f"Best genome saved to {filename}")
        return genome_data

def run_ultimate_evolution():
    """Main evolution menu"""
    print("🧬 ULTIMATE AI EVOLUTION SYSTEM 🧬")
    print("Choose your evolution type:")
    print()
    print("1. Basic Evolution - Evolve generation parameters")
    print("2. Smart Evolution - Evolve for intelligence")
    print("3. Model Evolution - Evolve to larger models")
    print("4. Growth Evolution - Evolve architecture size")
    print("5. Load saved genome and chat")
    print()

    choice = input("Enter choice (1-5): ").strip()

    if choice == "1":
        evolution_type = "basic"
    elif choice == "2":
        evolution_type = "smart"
    elif choice == "3":
        evolution_type = "model"
    elif choice == "4":
        evolution_type = "growth"
    elif choice == "5":
        return load_and_chat()
    else:
        print("Invalid choice, using basic evolution...")
        evolution_type = "basic"

    # Test inputs for evolution
    test_inputs = [
        "Hello, how are you?",
        "What do you think about artificial intelligence?",
        "Tell me something interesting about technology",
        "How would you solve a complex problem?",
        "What's your opinion on the future?",
        "Explain something you find fascinating",
        "How do you feel about creativity?",
        "What makes a good conversation?"
    ]

    print(f"\n🚀 Starting {evolution_type} evolution...")

    # Create evolution system
    population_size = 6 if evolution_type in ["basic", "smart"] else 4  # Smaller for larger models
    evolution = UltimateEvolution(population_size=population_size, evolution_type=evolution_type)
    evolution.create_initial_population()

    # Run evolution
    num_generations = 4 if evolution_type == "growth" else 5
    for gen in range(num_generations):
        print(f"\n=== GENERATION {gen + 1} ===")
        evolution.evolve_generation(test_inputs)

    # Get best AI
    best_ai = evolution.get_best_ai()
    print(f"\n🏆 EVOLUTION COMPLETE!")
    print(f"Best AI: {best_ai.genome.id}")
    print(f"Evolution Type: {best_ai.genome.evolution_type}")
    print(f"Fitness: {best_ai.genome.fitness:.3f}")

    if evolution_type == "smart":
        print(f"Intelligence Score: {best_ai.genome.intelligence_score:.3f}")
        print(f"Model: {best_ai.genome.model_name} (Level {best_ai.genome.intelligence_level})")
    elif evolution_type == "model":
        print(f"Model: {best_ai.genome.model_name}")
        print(f"Estimated Parameters: {best_ai.genome.estimated_params:,}")
    elif evolution_type == "growth":
        print(f"Architecture: {best_ai.genome.n_layers} layers, {best_ai.genome.n_embd} embedding")
        print(f"Estimated Parameters: {best_ai.genome.estimated_params:,}")

    # Save best genome
    filename = f"best_{evolution_type}_genome.json"
    evolution.save_best_genome(filename)

    return best_ai

def load_and_chat():
    """Load a saved genome and chat with it"""
    import os

    # Find available genome files
    genome_files = [f for f in os.listdir('.') if f.endswith('_genome.json')]

    if not genome_files:
        print("No saved genomes found!")
        return None

    print("Available saved genomes:")
    for i, filename in enumerate(genome_files):
        print(f"{i+1}. {filename}")

    try:
        choice = int(input("Choose genome to load: ")) - 1
        filename = genome_files[choice]

        with open(filename, 'r') as f:
            genome_data = json.load(f)

        genome = UltimateGenome(**genome_data)
        ai = UltimateAI(genome)

        print(f"Loaded genome: {genome.id}")
        print(f"Evolution type: {genome.evolution_type}")
        print(f"Fitness: {genome.fitness:.3f}")

        return ai

    except (ValueError, IndexError, FileNotFoundError) as e:
        print(f"Error loading genome: {e}")
        return None

def chat_with_ai(ai: UltimateAI):
    """Interactive chat with evolved AI"""
    print(f"\n🤖 CHATTING WITH EVOLVED AI: {ai.genome.id}")
    print(f"Evolution Type: {ai.genome.evolution_type}")
    print(f"Fitness: {ai.genome.fitness:.3f}")
    print("Type 'quit' to exit, 'info' for genome details, 'save' to save genome")
    print("=" * 60)

    while True:
        try:
            user_input = input("\nYou: ")

            if user_input.lower() == "quit":
                print("Goodbye! Thanks for chatting! 🤖")
                break
            elif user_input.lower() == "info":
                show_genome_info(ai.genome)
                continue
            elif user_input.lower() == "save":
                filename = f"chat_genome_{int(time.time())}.json"
                with open(filename, 'w') as f:
                    json.dump(asdict(ai.genome), f, indent=2)
                print(f"Genome saved to {filename}")
                continue

            response = ai.generate_response(user_input)
            print(f"AI: {response}")

        except KeyboardInterrupt:
            print("\n\nGoodbye! Thanks for chatting! 🤖")
            break
        except Exception as e:
            print(f"Error: {e}")
            print("Let's try again...")

def show_genome_info(genome: UltimateGenome):
    """Display detailed genome information"""
    print("\n" + "=" * 50)
    print("🧬 GENOME DETAILS")
    print("=" * 50)
    print(f"ID: {genome.id}")
    print(f"Evolution Type: {genome.evolution_type}")
    print(f"Generation: {genome.generation}")
    print(f"Fitness: {genome.fitness:.6f}")
    print()
    print("Parameters:")
    print(f"  Temperature: {genome.temperature:.3f}")
    print(f"  Top-K: {genome.top_k}")
    print(f"  Top-P: {genome.top_p:.3f}")
    print(f"  Repetition Penalty: {genome.repetition_penalty:.3f}")
    print(f"  Response Length: {genome.min_response_length}-{genome.max_response_length}")
    print(f"  Beam Search: {genome.num_beams}")
    print()
    print(f"Model: {genome.model_name}")
    print(f"Intelligence Level: {genome.intelligence_level}")
    if genome.evolution_type == "growth":
        print(f"Architecture: {genome.n_layers} layers, {genome.n_embd} embd, {genome.n_heads} heads")
        print(f"Estimated Parameters: {genome.estimated_params:,}")
    print("=" * 50)

if __name__ == "__main__":
    print("🧬 ULTIMATE AI EVOLUTION SYSTEM 🧬")
    print("The one and only script for evolving AI!")
    print()

    # Run evolution
    best_ai = run_ultimate_evolution()

    if best_ai:
        # Chat with the evolved AI
        chat_with_ai(best_ai)
