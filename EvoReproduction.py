from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import random
import json
import copy
from dataclasses import dataclass, asdict
from typing import List, Dict, Any
import numpy as np

@dataclass
class ChatbotGenome:
    """Genome defining chatbot behavior parameters"""
    temperature: float = 0.7
    top_k: int = 50
    top_p: float = 0.95
    repetition_penalty: float = 1.1
    max_response_length: int = 50
    fitness: float = 0.0
    generation: int = 0
    id: str = ""

class EvolutionaryChatbot:
    """A chatbot that can evolve its parameters"""

    def __init__(self, genome: ChatbotGenome, model_name: str = "gpt2"):
        self.genome = genome
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.conversation_history = None

    def load_model(self):
        """Load the transformer model"""
        if self.model is None:
            print(f"Loading model: {self.model_name}")
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                self.model = AutoModelForCausalLM.from_pretrained(self.model_name)

                if self.tokenizer.pad_token is None:
                    self.tokenizer.pad_token = self.tokenizer.eos_token

            except Exception as e:
                print(f"Failed to load {self.model_name}, falling back to gpt2: {e}")
                self.model_name = "gpt2"
                self.tokenizer = AutoTokenizer.from_pretrained("gpt2")
                self.model = AutoModelForCausalLM.from_pretrained("gpt2")
                self.tokenizer.pad_token = self.tokenizer.eos_token

    def generate_response(self, user_input: str) -> str:
        """Generate response using the chatbot's evolved parameters"""
        if self.model is None:
            self.load_model()

        # Format input
        if "gpt2" in self.model_name.lower():
            # Use conversation format for GPT-2
            prompt = f"Human: {user_input}\nAssistant:"
        else:
            # Use DialoGPT format
            new_user_input_ids = self.tokenizer.encode(user_input + self.tokenizer.eos_token, return_tensors="pt")

            if self.conversation_history is not None:
                bot_input_ids = torch.cat([self.conversation_history, new_user_input_ids], dim=-1)
            else:
                bot_input_ids = new_user_input_ids

            # Generate with evolved parameters
            with torch.no_grad():
                self.conversation_history = self.model.generate(
                    bot_input_ids,
                    max_length=bot_input_ids.shape[-1] + self.genome.max_response_length,
                    do_sample=True,
                    temperature=self.genome.temperature,
                    top_k=self.genome.top_k,
                    top_p=self.genome.top_p,
                    repetition_penalty=self.genome.repetition_penalty,
                    pad_token_id=self.tokenizer.eos_token_id
                )

            # Extract response
            bot_response = self.conversation_history[:, bot_input_ids.shape[-1]:]
            response = self.tokenizer.decode(bot_response[0], skip_special_tokens=True)
            return response.strip()

        # GPT-2 handling
        input_ids = self.tokenizer.encode(prompt, return_tensors="pt")

        with torch.no_grad():
            output = self.model.generate(
                input_ids,
                max_length=input_ids.shape[-1] + self.genome.max_response_length,
                do_sample=True,
                temperature=self.genome.temperature,
                top_k=self.genome.top_k,
                top_p=self.genome.top_p,
                repetition_penalty=self.genome.repetition_penalty,
                pad_token_id=self.tokenizer.eos_token_id
            )

        response = self.tokenizer.decode(output[0], skip_special_tokens=True)

        if "Assistant:" in response:
            bot_response = response.split("Assistant:")[-1].strip()
            if "Human:" in bot_response:
                bot_response = bot_response.split("Human:")[0].strip()
        else:
            bot_response = "I'm not sure how to respond."

        return bot_response.strip()

class ChatbotEvolution:
    """Manages evolution of chatbot population"""

    def __init__(self, population_size: int = 6, model_name: str = "gpt2"):
        self.population_size = population_size
        self.model_name = model_name
        self.population: List[EvolutionaryChatbot] = []
        self.generation = 0

    def create_initial_population(self):
        """Create initial random population"""
        print(f"Creating initial population of {self.population_size} chatbots...")

        for i in range(self.population_size):
            genome = ChatbotGenome(
                temperature=random.uniform(0.3, 1.2),
                top_k=random.randint(20, 100),
                top_p=random.uniform(0.7, 0.99),
                repetition_penalty=random.uniform(1.0, 1.5),
                max_response_length=random.randint(20, 80),
                generation=0,
                id=f"gen0_bot{i}"
            )

            chatbot = EvolutionaryChatbot(genome, self.model_name)
            self.population.append(chatbot)

        print("Population created!")

    def evaluate_fitness(self, test_inputs: List[str]):
        """Evaluate fitness of all chatbots"""
        print(f"Evaluating generation {self.generation}...")

        for i, chatbot in enumerate(self.population):
            print(f"Testing chatbot {i+1}/{len(self.population)}")
            fitness_scores = []

            for test_input in test_inputs:
                try:
                    response = chatbot.generate_response(test_input)
                    fitness = self.calculate_fitness(response, test_input)
                    fitness_scores.append(fitness)
                except Exception as e:
                    print(f"Error with chatbot {i}: {e}")
                    fitness_scores.append(0.0)

            chatbot.genome.fitness = np.mean(fitness_scores) if fitness_scores else 0.0
            print(f"Chatbot {i} fitness: {chatbot.genome.fitness:.3f}")

    def calculate_fitness(self, response: str, user_input: str) -> float:
        """Calculate fitness score for a response"""
        if not response or len(response.strip()) == 0:
            return 0.0

        # Length fitness (prefer moderate length)
        length_score = max(0.0, 1.0 - abs(len(response) - 50) / 100.0)

        # Diversity fitness (unique words)
        words = response.lower().split()
        if len(words) > 0:
            diversity_score = len(set(words)) / len(words)
        else:
            diversity_score = 0.0

        # Coherence fitness (ends properly)
        coherence_score = 1.0 if response.strip().endswith(('.', '!', '?')) else 0.5

        # Combine scores
        return (length_score * 0.4 + diversity_score * 0.4 + coherence_score * 0.2)

    def evolve_generation(self, test_inputs: List[str]):
        """Evolve to next generation"""
        # Evaluate current population
        self.evaluate_fitness(test_inputs)

        # Sort by fitness
        self.population.sort(key=lambda x: x.genome.fitness, reverse=True)

        # Show stats
        fitnesses = [bot.genome.fitness for bot in self.population]
        print(f"Generation {self.generation} - Best: {max(fitnesses):.3f}, Avg: {np.mean(fitnesses):.3f}")

        # Create next generation
        new_population = []

        # Keep best (elitism)
        elite_count = max(1, self.population_size // 3)
        for i in range(elite_count):
            elite = copy.deepcopy(self.population[i])
            elite.genome.generation = self.generation + 1
            elite.genome.id = f"gen{self.generation + 1}_elite{i}"
            elite.model = None  # Reset model to save memory
            new_population.append(elite)

        # Create offspring
        while len(new_population) < self.population_size:
            # Select parents
            parent1 = self.tournament_selection()
            parent2 = self.tournament_selection()

            # Create child
            child_genome = self.crossover(parent1.genome, parent2.genome)
            child_genome = self.mutate(child_genome)
            child_genome.generation = self.generation + 1
            child_genome.id = f"gen{self.generation + 1}_child{len(new_population)}"
            child_genome.fitness = 0.0

            child = EvolutionaryChatbot(child_genome, self.model_name)
            new_population.append(child)

        self.population = new_population
        self.generation += 1

    def tournament_selection(self, tournament_size: int = 3) -> EvolutionaryChatbot:
        """Select parent using tournament selection"""
        tournament = random.sample(self.population, min(tournament_size, len(self.population)))
        return max(tournament, key=lambda x: x.genome.fitness)

    def crossover(self, parent1: ChatbotGenome, parent2: ChatbotGenome) -> ChatbotGenome:
        """Create child through crossover"""
        child = ChatbotGenome()

        # Randomly inherit traits
        child.temperature = parent1.temperature if random.random() < 0.5 else parent2.temperature
        child.top_k = parent1.top_k if random.random() < 0.5 else parent2.top_k
        child.top_p = parent1.top_p if random.random() < 0.5 else parent2.top_p
        child.repetition_penalty = parent1.repetition_penalty if random.random() < 0.5 else parent2.repetition_penalty
        child.max_response_length = parent1.max_response_length if random.random() < 0.5 else parent2.max_response_length

        return child

    def mutate(self, genome: ChatbotGenome, mutation_rate: float = 0.2) -> ChatbotGenome:
        """Apply mutations"""
        mutated = copy.deepcopy(genome)

        if random.random() < mutation_rate:
            mutated.temperature = max(0.1, min(2.0, mutated.temperature + random.gauss(0, 0.1)))

        if random.random() < mutation_rate:
            mutated.top_k = max(1, min(100, mutated.top_k + random.randint(-10, 10)))

        if random.random() < mutation_rate:
            mutated.top_p = max(0.1, min(1.0, mutated.top_p + random.gauss(0, 0.05)))

        if random.random() < mutation_rate:
            mutated.repetition_penalty = max(1.0, min(2.0, mutated.repetition_penalty + random.gauss(0, 0.05)))

        if random.random() < mutation_rate:
            mutated.max_response_length = max(10, min(100, mutated.max_response_length + random.randint(-10, 10)))

        return mutated

    def get_best_chatbot(self) -> EvolutionaryChatbot:
        """Get the best performing chatbot"""
        return max(self.population, key=lambda x: x.genome.fitness)

def run_evolution():
    """Run the evolution experiment"""
    test_inputs = [
        "Hello, how are you?",
        "What do you think about music?",
        "Tell me something interesting",
        "How do you feel?",
        "What's your favorite thing?"
    ]

    # Create evolution system
    evolution = ChatbotEvolution(population_size=4, model_name="gpt2")  # Small population for testing
    evolution.create_initial_population()

    # Run evolution for a few generations
    for gen in range(3):
        print(f"\n=== GENERATION {gen + 1} ===")
        evolution.evolve_generation(test_inputs)

    # Get best chatbot
    best_bot = evolution.get_best_chatbot()
    print(f"\nBest chatbot: {best_bot.genome.id} (fitness: {best_bot.genome.fitness:.3f})")
    print(f"Best genome: {asdict(best_bot.genome)}")

    return best_bot

def test_chatbot(chatbot: EvolutionaryChatbot):
    """Test the evolved chatbot interactively"""
    print("\n=== TESTING EVOLVED CHATBOT ===")
    print("Talk to the evolved bot! (type 'quit' to exit)")

    while True:
        try:
            user_input = input("You: ")
            if user_input.lower() == "quit":
                break

            response = chatbot.generate_response(user_input)
            print(f"Bot: {response}")

        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    print("Starting Evolutionary Chatbot Reproduction...")

    # Run evolution
    best_chatbot = run_evolution()

    # Test the best evolved chatbot
    test_chatbot(best_chatbot)
