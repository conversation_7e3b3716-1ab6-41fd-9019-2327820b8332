import random
import json
import copy
from typing import List, Dict, <PERSON>, <PERSON><PERSON>
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import numpy as np
from dataclasses import dataclass, asdict
import time

@dataclass
class ChatbotGenome:
    """Represents the genetic makeup of a chatbot"""
    # Generation parameters
    temperature: float = 0.7
    top_k: int = 50
    top_p: float = 0.95
    repetition_penalty: float = 1.1
    no_repeat_ngram_size: int = 3
    max_response_length: int = 50
    
    # Conversation parameters
    conversation_memory_limit: int = 400
    response_cleanup_threshold: float = 0.5
    
    # Model selection
    model_name: str = "gpt2"
    
    # Fitness score (calculated during evaluation)
    fitness: float = 0.0
    generation: int = 0
    id: str = ""

class EvolutionaryChatbot:
    """A single chatbot instance with its genome"""
    
    def __init__(self, genome: ChatbotGenome):
        self.genome = genome
        self.model = None
        self.tokenizer = None
        self.conversation_history = ""
        self.response_count = 0
        
    def load_model(self):
        """Load the model and tokenizer"""
        if self.model is None:
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(self.genome.model_name)
                self.model = AutoModelForCausalLM.from_pretrained(self.genome.model_name)
                
                if self.tokenizer.pad_token is None:
                    self.tokenizer.pad_token = self.tokenizer.eos_token
                    
            except Exception as e:
                print(f"Failed to load model {self.genome.model_name}: {e}")
                # Fallback to GPT-2
                self.genome.model_name = "gpt2"
                self.tokenizer = AutoTokenizer.from_pretrained("gpt2")
                self.model = AutoModelForCausalLM.from_pretrained("gpt2")
                self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def generate_response(self, user_input: str) -> str:
        """Generate a response using the chatbot's genome parameters"""
        if self.model is None:
            self.load_model()
        
        # Format conversation
        if self.conversation_history:
            prompt = f"{self.conversation_history}\nHuman: {user_input}\nAssistant:"
        else:
            prompt = f"Human: {user_input}\nAssistant:"
        
        # Encode input
        input_ids = self.tokenizer.encode(prompt, return_tensors="pt")
        
        # Limit input length
        if input_ids.shape[-1] > self.genome.conversation_memory_limit:
            input_ids = input_ids[:, -self.genome.conversation_memory_limit:]
        
        # Generate response
        with torch.no_grad():
            output = self.model.generate(
                input_ids,
                max_length=input_ids.shape[-1] + self.genome.max_response_length,
                do_sample=True,
                temperature=self.genome.temperature,
                top_k=self.genome.top_k,
                top_p=self.genome.top_p,
                repetition_penalty=self.genome.repetition_penalty,
                no_repeat_ngram_size=self.genome.no_repeat_ngram_size,
                pad_token_id=self.tokenizer.eos_token_id
            )
        
        # Extract response
        response = self.tokenizer.decode(output[0], skip_special_tokens=True)
        
        if "Assistant:" in response:
            bot_response = response.split("Assistant:")[-1].strip()
            if "Human:" in bot_response:
                bot_response = bot_response.split("Human:")[0].strip()
        else:
            bot_response = "I'm not sure how to respond."
        
        # Clean up response
        bot_response = bot_response.strip()
        if not bot_response:
            bot_response = "Could you please rephrase that?"
        
        # Update conversation history
        self.conversation_history = f"{self.conversation_history}\nHuman: {user_input}\nAssistant: {bot_response}"
        
        # Trim history if too long
        if len(self.conversation_history) > self.genome.conversation_memory_limit * 2:
            lines = self.conversation_history.split('\n')
            self.conversation_history = '\n'.join(lines[-8:])
        
        self.response_count += 1
        return bot_response

class ChatbotPopulation:
    """Manages a population of evolutionary chatbots"""
    
    def __init__(self, population_size: int = 10):
        self.population_size = population_size
        self.population: List[EvolutionaryChatbot] = []
        self.generation = 0
        self.fitness_history = []
        
    def initialize_population(self):
        """Create initial random population"""
        print(f"Initializing population of {self.population_size} chatbots...")
        
        for i in range(self.population_size):
            genome = ChatbotGenome(
                temperature=random.uniform(0.3, 1.2),
                top_k=random.randint(20, 100),
                top_p=random.uniform(0.7, 0.99),
                repetition_penalty=random.uniform(1.0, 1.5),
                no_repeat_ngram_size=random.randint(2, 5),
                max_response_length=random.randint(20, 80),
                conversation_memory_limit=random.randint(200, 600),
                generation=0,
                id=f"gen0_bot{i}"
            )
            
            chatbot = EvolutionaryChatbot(genome)
            self.population.append(chatbot)
        
        print("Population initialized!")
    
    def evaluate_fitness(self, test_conversations: List[str]) -> None:
        """Evaluate fitness of all chatbots in population"""
        print(f"Evaluating fitness for generation {self.generation}...")
        
        for i, chatbot in enumerate(self.population):
            print(f"Evaluating chatbot {i+1}/{len(self.population)}")
            fitness_scores = []
            
            for conversation_starter in test_conversations:
                # Reset chatbot state
                chatbot.conversation_history = ""
                chatbot.response_count = 0
                
                try:
                    # Have a short conversation
                    response1 = chatbot.generate_response(conversation_starter)
                    response2 = chatbot.generate_response("That's interesting. Tell me more.")
                    response3 = chatbot.generate_response("What do you think about that?")
                    
                    # Calculate fitness based on various criteria
                    fitness = self.calculate_response_fitness(
                        [response1, response2, response3],
                        [conversation_starter, "That's interesting. Tell me more.", "What do you think about that?"]
                    )
                    fitness_scores.append(fitness)
                    
                except Exception as e:
                    print(f"Error evaluating chatbot {i}: {e}")
                    fitness_scores.append(0.0)
            
            # Average fitness across all test conversations
            chatbot.genome.fitness = np.mean(fitness_scores) if fitness_scores else 0.0
            print(f"Chatbot {i} fitness: {chatbot.genome.fitness:.3f}")
    
    def calculate_response_fitness(self, responses: List[str], inputs: List[str]) -> float:
        """Calculate fitness score for a set of responses"""
        fitness = 0.0
        
        for response, user_input in zip(responses, inputs):
            # Length appropriateness (not too short, not too long)
            length_score = 1.0 - abs(len(response) - 50) / 100.0
            length_score = max(0.0, min(1.0, length_score))
            
            # Diversity (avoid repetitive responses)
            diversity_score = 1.0
            words = response.lower().split()
            if len(words) > 1:
                unique_words = len(set(words))
                diversity_score = unique_words / len(words)
            
            # Relevance (simple heuristic - avoid completely random responses)
            relevance_score = 0.5  # Base score
            if len(response) > 5 and not response.startswith("I'm not sure"):
                relevance_score = 0.8
            
            # Coherence (avoid truncated or malformed responses)
            coherence_score = 1.0
            if response.endswith(("...", ".", "!", "?")):
                coherence_score = 1.0
            else:
                coherence_score = 0.7
            
            # Combine scores
            response_fitness = (length_score * 0.3 + 
                             diversity_score * 0.3 + 
                             relevance_score * 0.2 + 
                             coherence_score * 0.2)
            
            fitness += response_fitness
        
        return fitness / len(responses)
    
    def select_parents(self, num_parents: int) -> List[EvolutionaryChatbot]:
        """Select parents for reproduction using tournament selection"""
        parents = []
        
        for _ in range(num_parents):
            # Tournament selection
            tournament_size = 3
            tournament = random.sample(self.population, min(tournament_size, len(self.population)))
            winner = max(tournament, key=lambda x: x.genome.fitness)
            parents.append(winner)
        
        return parents
    
    def crossover(self, parent1: ChatbotGenome, parent2: ChatbotGenome) -> ChatbotGenome:
        """Create offspring through crossover"""
        child = ChatbotGenome()
        
        # Randomly inherit traits from parents
        for field_name in ['temperature', 'top_k', 'top_p', 'repetition_penalty', 
                          'no_repeat_ngram_size', 'max_response_length', 'conversation_memory_limit']:
            if random.random() < 0.5:
                setattr(child, field_name, getattr(parent1, field_name))
            else:
                setattr(child, field_name, getattr(parent2, field_name))
        
        # Inherit model from fitter parent
        if parent1.fitness >= parent2.fitness:
            child.model_name = parent1.model_name
        else:
            child.model_name = parent2.model_name
        
        return child
    
    def mutate(self, genome: ChatbotGenome, mutation_rate: float = 0.1) -> ChatbotGenome:
        """Apply mutations to genome"""
        mutated = copy.deepcopy(genome)
        
        if random.random() < mutation_rate:
            mutated.temperature = max(0.1, min(2.0, mutated.temperature + random.gauss(0, 0.1)))
        
        if random.random() < mutation_rate:
            mutated.top_k = max(1, min(100, mutated.top_k + random.randint(-10, 10)))
        
        if random.random() < mutation_rate:
            mutated.top_p = max(0.1, min(1.0, mutated.top_p + random.gauss(0, 0.05)))
        
        if random.random() < mutation_rate:
            mutated.repetition_penalty = max(1.0, min(2.0, mutated.repetition_penalty + random.gauss(0, 0.05)))
        
        if random.random() < mutation_rate:
            mutated.no_repeat_ngram_size = max(1, min(10, mutated.no_repeat_ngram_size + random.randint(-1, 1)))
        
        if random.random() < mutation_rate:
            mutated.max_response_length = max(10, min(100, mutated.max_response_length + random.randint(-10, 10)))
        
        if random.random() < mutation_rate:
            mutated.conversation_memory_limit = max(100, min(1000, mutated.conversation_memory_limit + random.randint(-50, 50)))
        
        return mutated
    
    def evolve_generation(self, test_conversations: List[str]):
        """Evolve to the next generation"""
        # Evaluate current population
        self.evaluate_fitness(test_conversations)
        
        # Record fitness statistics
        fitnesses = [bot.genome.fitness for bot in self.population]
        avg_fitness = np.mean(fitnesses)
        max_fitness = np.max(fitnesses)
        self.fitness_history.append({'generation': self.generation, 'avg': avg_fitness, 'max': max_fitness})
        
        print(f"Generation {self.generation} - Avg fitness: {avg_fitness:.3f}, Max fitness: {max_fitness:.3f}")
        
        # Select parents (keep top 50% as potential parents)
        self.population.sort(key=lambda x: x.genome.fitness, reverse=True)
        num_parents = max(2, self.population_size // 2)
        parents = self.population[:num_parents]
        
        # Create new generation
        new_population = []
        
        # Keep best individuals (elitism)
        elite_count = max(1, self.population_size // 5)
        for i in range(elite_count):
            elite = copy.deepcopy(parents[i])
            elite.genome.generation = self.generation + 1
            elite.genome.id = f"gen{self.generation + 1}_elite{i}"
            new_population.append(elite)
        
        # Generate offspring
        while len(new_population) < self.population_size:
            parent1, parent2 = random.sample(parents, 2)
            child_genome = self.crossover(parent1.genome, parent2.genome)
            child_genome = self.mutate(child_genome)
            child_genome.generation = self.generation + 1
            child_genome.id = f"gen{self.generation + 1}_child{len(new_population)}"
            child_genome.fitness = 0.0
            
            child_bot = EvolutionaryChatbot(child_genome)
            new_population.append(child_bot)
        
        self.population = new_population
        self.generation += 1
    
    def get_best_chatbot(self) -> EvolutionaryChatbot:
        """Get the best performing chatbot"""
        return max(self.population, key=lambda x: x.genome.fitness)
    
    def save_population(self, filename: str):
        """Save population to file"""
        data = {
            'generation': self.generation,
            'fitness_history': self.fitness_history,
            'population': [asdict(bot.genome) for bot in self.population]
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_population(self, filename: str):
        """Load population from file"""
        with open(filename, 'r') as f:
            data = json.load(f)
        
        self.generation = data['generation']
        self.fitness_history = data['fitness_history']
        
        self.population = []
        for genome_data in data['population']:
            genome = ChatbotGenome(**genome_data)
            bot = EvolutionaryChatbot(genome)
            self.population.append(bot)

def run_evolution_experiment():
    """Run a complete evolution experiment"""
    # Test conversations for fitness evaluation
    test_conversations = [
        "Hello, how are you?",
        "What's your favorite color?",
        "Tell me a joke",
        "What do you think about artificial intelligence?",
        "How's the weather?",
        "What's your name?",
        "Can you help me with something?",
        "What's 2 + 2?",
        "Do you like music?",
        "What's your opinion on books?"
    ]
    
    # Create population
    population = ChatbotPopulation(population_size=8)  # Smaller for faster testing
    population.initialize_population()
    
    # Run evolution for several generations
    num_generations = 5
    
    for gen in range(num_generations):
        print(f"\n=== GENERATION {gen + 1} ===")
        population.evolve_generation(test_conversations)
        
        # Save progress
        population.save_population(f"population_gen_{population.generation}.json")
        
        # Show best chatbot
        best_bot = population.get_best_chatbot()
        print(f"Best chatbot: {best_bot.genome.id} (fitness: {best_bot.genome.fitness:.3f})")
        print(f"Best genome: {asdict(best_bot.genome)}")
    
    return population

if __name__ == "__main__":
    print("Starting Evolutionary Chatbot Experiment...")
    final_population = run_evolution_experiment()
    
    print("\n=== EVOLUTION COMPLETE ===")
    best_chatbot = final_population.get_best_chatbot()
    print(f"Final best chatbot: {best_chatbot.genome.id}")
    print(f"Final fitness: {best_chatbot.genome.fitness:.3f}")
    
    # Test the best chatbot
    print("\n=== TESTING BEST CHATBOT ===")
    print("You can now chat with the evolved chatbot!")
    print("Type 'quit' to exit")
    
    while True:
        user_input = input("You: ")
        if user_input.lower() == 'quit':
            break
        
        response = best_chatbot.generate_response(user_input)
        print(f"Bot: {response}")
