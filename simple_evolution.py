import random
import json
import copy
from dataclasses import dataclass, asdict
from typing import List, Dict, Any
import numpy as np

@dataclass
class SimpleBotGenome:
    """Simple genome for chatbot evolution"""
    # Core parameters that affect behavior
    temperature: float = 0.7
    max_length: int = 50
    top_k: int = 50
    top_p: float = 0.95
    
    # Fitness and metadata
    fitness: float = 0.0
    generation: int = 0
    id: str = ""
    parent_ids: List[str] = None
    
    def __post_init__(self):
        if self.parent_ids is None:
            self.parent_ids = []

class SimpleBot:
    """A simple bot with basic response generation"""
    
    def __init__(self, genome: SimpleBotGenome):
        self.genome = genome
        self.response_templates = [
            "That's interesting! I think {topic} is really {adjective}.",
            "I {feeling} about {topic}. What do you think?",
            "From my perspective, {topic} seems {adjective}.",
            "I've been thinking about {topic} lately. It's quite {adjective}.",
            "You know, {topic} reminds me of {other_topic}.",
            "I {feeling} when people talk about {topic}.",
            "That's a {adjective} point about {topic}.",
            "I wonder if {topic} will become more {adjective} in the future.",
        ]
        
        self.topics = ["technology", "music", "books", "nature", "art", "science", "food", "travel"]
        self.adjectives = ["fascinating", "complex", "beautiful", "important", "challenging", "exciting", "mysterious", "wonderful"]
        self.feelings = ["get excited", "feel curious", "become thoughtful", "get interested", "feel passionate"]
        self.other_topics = ["philosophy", "creativity", "human nature", "the future", "learning", "growth"]
    
    def generate_response(self, user_input: str) -> str:
        """Generate a response based on genome parameters"""
        # Simple response generation using templates
        
        # Use temperature to affect randomness
        if random.random() < self.genome.temperature:
            # More random/creative response
            template = random.choice(self.response_templates)
            topic = random.choice(self.topics)
            adjective = random.choice(self.adjectives)
            feeling = random.choice(self.feelings)
            other_topic = random.choice(self.other_topics)
            
            response = template.format(
                topic=topic,
                adjective=adjective,
                feeling=feeling,
                other_topic=other_topic
            )
        else:
            # More conservative response
            responses = [
                "That's interesting.",
                "I see what you mean.",
                "Could you tell me more?",
                "That's a good point.",
                "I understand.",
            ]
            response = random.choice(responses)
        
        # Apply max_length constraint
        words = response.split()
        if len(words) > self.genome.max_length // 5:  # Rough word count estimate
            response = " ".join(words[:self.genome.max_length // 5])
        
        return response

class EvolutionEngine:
    """Manages the evolution of bot populations"""
    
    def __init__(self, population_size: int = 10):
        self.population_size = population_size
        self.population: List[SimpleBot] = []
        self.generation = 0
        self.evolution_log = []
    
    def create_initial_population(self):
        """Create the first generation with random genomes"""
        print(f"Creating initial population of {self.population_size} bots...")
        
        for i in range(self.population_size):
            genome = SimpleBotGenome(
                temperature=random.uniform(0.1, 1.0),
                max_length=random.randint(20, 100),
                top_k=random.randint(10, 100),
                top_p=random.uniform(0.5, 0.99),
                generation=0,
                id=f"gen0_bot{i:03d}"
            )
            
            bot = SimpleBot(genome)
            self.population.append(bot)
        
        print("Initial population created!")
    
    def evaluate_fitness(self, test_inputs: List[str]):
        """Evaluate fitness of all bots in the population"""
        print(f"Evaluating fitness for generation {self.generation}...")
        
        for i, bot in enumerate(self.population):
            fitness_scores = []
            
            for test_input in test_inputs:
                try:
                    response = bot.generate_response(test_input)
                    
                    # Simple fitness calculation
                    fitness = self.calculate_response_fitness(response, test_input)
                    fitness_scores.append(fitness)
                    
                except Exception as e:
                    print(f"Error evaluating bot {i}: {e}")
                    fitness_scores.append(0.0)
            
            # Average fitness across all test inputs
            bot.genome.fitness = np.mean(fitness_scores) if fitness_scores else 0.0
            
        # Sort population by fitness
        self.population.sort(key=lambda x: x.genome.fitness, reverse=True)
        
        # Log generation statistics
        fitnesses = [bot.genome.fitness for bot in self.population]
        stats = {
            'generation': self.generation,
            'avg_fitness': np.mean(fitnesses),
            'max_fitness': np.max(fitnesses),
            'min_fitness': np.min(fitnesses),
            'best_bot_id': self.population[0].genome.id
        }
        self.evolution_log.append(stats)
        
        print(f"Generation {self.generation} stats:")
        print(f"  Best fitness: {stats['max_fitness']:.3f} (Bot: {stats['best_bot_id']})")
        print(f"  Average fitness: {stats['avg_fitness']:.3f}")
        print(f"  Worst fitness: {stats['min_fitness']:.3f}")
    
    def calculate_response_fitness(self, response: str, user_input: str) -> float:
        """Calculate fitness score for a single response"""
        fitness = 0.0
        
        # Length fitness (prefer moderate length responses)
        length = len(response)
        if 10 <= length <= 100:
            length_fitness = 1.0
        elif length < 10:
            length_fitness = length / 10.0
        else:
            length_fitness = max(0.0, 1.0 - (length - 100) / 100.0)
        
        # Diversity fitness (prefer responses with varied vocabulary)
        words = response.lower().split()
        if len(words) > 0:
            unique_words = len(set(words))
            diversity_fitness = min(1.0, unique_words / len(words))
        else:
            diversity_fitness = 0.0
        
        # Coherence fitness (simple check for complete sentences)
        coherence_fitness = 1.0 if response.strip().endswith(('.', '!', '?')) else 0.5
        
        # Relevance fitness (very basic - just check if response isn't empty)
        relevance_fitness = 1.0 if response.strip() else 0.0
        
        # Combine fitness components
        fitness = (length_fitness * 0.3 + 
                  diversity_fitness * 0.3 + 
                  coherence_fitness * 0.2 + 
                  relevance_fitness * 0.2)
        
        return fitness
    
    def reproduce(self) -> List[SimpleBot]:
        """Create next generation through reproduction"""
        print("Creating next generation...")
        
        new_population = []
        
        # Elitism: Keep top 20% of population
        elite_count = max(1, self.population_size // 5)
        for i in range(elite_count):
            elite_bot = copy.deepcopy(self.population[i])
            elite_bot.genome.generation = self.generation + 1
            elite_bot.genome.id = f"gen{self.generation + 1}_elite{i:03d}"
            new_population.append(elite_bot)
        
        # Fill rest with offspring
        while len(new_population) < self.population_size:
            # Select parents using tournament selection
            parent1 = self.tournament_selection()
            parent2 = self.tournament_selection()
            
            # Create offspring through crossover and mutation
            child_genome = self.crossover(parent1.genome, parent2.genome)
            child_genome = self.mutate(child_genome)
            
            # Set child metadata
            child_genome.generation = self.generation + 1
            child_genome.id = f"gen{self.generation + 1}_child{len(new_population):03d}"
            child_genome.parent_ids = [parent1.genome.id, parent2.genome.id]
            child_genome.fitness = 0.0
            
            child_bot = SimpleBot(child_genome)
            new_population.append(child_bot)
        
        return new_population
    
    def tournament_selection(self, tournament_size: int = 3) -> SimpleBot:
        """Select a parent using tournament selection"""
        tournament = random.sample(self.population, min(tournament_size, len(self.population)))
        return max(tournament, key=lambda x: x.genome.fitness)
    
    def crossover(self, parent1: SimpleBotGenome, parent2: SimpleBotGenome) -> SimpleBotGenome:
        """Create offspring genome through crossover"""
        child = SimpleBotGenome()
        
        # Randomly inherit each trait from one parent or the other
        child.temperature = parent1.temperature if random.random() < 0.5 else parent2.temperature
        child.max_length = parent1.max_length if random.random() < 0.5 else parent2.max_length
        child.top_k = parent1.top_k if random.random() < 0.5 else parent2.top_k
        child.top_p = parent1.top_p if random.random() < 0.5 else parent2.top_p
        
        return child
    
    def mutate(self, genome: SimpleBotGenome, mutation_rate: float = 0.2) -> SimpleBotGenome:
        """Apply mutations to genome"""
        mutated = copy.deepcopy(genome)
        
        if random.random() < mutation_rate:
            # Mutate temperature
            mutated.temperature = max(0.1, min(1.0, mutated.temperature + random.gauss(0, 0.1)))
        
        if random.random() < mutation_rate:
            # Mutate max_length
            mutated.max_length = max(10, min(200, mutated.max_length + random.randint(-10, 10)))
        
        if random.random() < mutation_rate:
            # Mutate top_k
            mutated.top_k = max(1, min(100, mutated.top_k + random.randint(-5, 5)))
        
        if random.random() < mutation_rate:
            # Mutate top_p
            mutated.top_p = max(0.1, min(0.99, mutated.top_p + random.gauss(0, 0.05)))
        
        return mutated
    
    def evolve_generation(self, test_inputs: List[str]):
        """Evolve one generation"""
        # Evaluate current population
        self.evaluate_fitness(test_inputs)
        
        # Create next generation
        self.population = self.reproduce()
        self.generation += 1
    
    def run_evolution(self, num_generations: int, test_inputs: List[str]):
        """Run evolution for multiple generations"""
        print(f"Starting evolution for {num_generations} generations...")
        
        for gen in range(num_generations):
            print(f"\n--- Generation {gen + 1}/{num_generations} ---")
            self.evolve_generation(test_inputs)
            
            # Save progress
            self.save_progress(f"evolution_gen_{self.generation}.json")
        
        print("\nEvolution complete!")
        return self.get_best_bot()
    
    def get_best_bot(self) -> SimpleBot:
        """Get the best performing bot"""
        return max(self.population, key=lambda x: x.genome.fitness)
    
    def save_progress(self, filename: str):
        """Save evolution progress to file"""
        data = {
            'generation': self.generation,
            'evolution_log': self.evolution_log,
            'population': [asdict(bot.genome) for bot in self.population]
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
    
    def test_bot_conversation(self, bot: SimpleBot, num_exchanges: int = 5):
        """Test a bot in conversation"""
        print(f"\nTesting bot {bot.genome.id} (fitness: {bot.genome.fitness:.3f})")
        print("=" * 50)
        
        test_inputs = [
            "Hello, how are you?",
            "What do you think about technology?",
            "Tell me something interesting",
            "What's your favorite thing?",
            "How do you feel about the future?"
        ]
        
        for i, user_input in enumerate(test_inputs[:num_exchanges]):
            response = bot.generate_response(user_input)
            print(f"Human: {user_input}")
            print(f"Bot: {response}")
            print()

def main():
    """Main evolution experiment"""
    # Test inputs for fitness evaluation
    test_inputs = [
        "Hello",
        "How are you?",
        "What do you think about music?",
        "Tell me a joke",
        "What's your opinion?",
        "Can you help me?",
        "What's interesting?",
        "How do you feel?",
        "What do you like?",
        "Tell me more"
    ]
    
    # Create evolution engine
    engine = EvolutionEngine(population_size=12)
    engine.create_initial_population()
    
    # Run evolution
    best_bot = engine.run_evolution(num_generations=5, test_inputs=test_inputs)
    
    # Test the best bot
    print("\n" + "="*60)
    print("EVOLUTION RESULTS")
    print("="*60)
    
    engine.test_bot_conversation(best_bot)
    
    print(f"Best bot genome: {asdict(best_bot.genome)}")
    
    # Show evolution progress
    print("\nEvolution Progress:")
    for log_entry in engine.evolution_log:
        print(f"Gen {log_entry['generation']}: "
              f"Best={log_entry['max_fitness']:.3f}, "
              f"Avg={log_entry['avg_fitness']:.3f}")

if __name__ == "__main__":
    main()
