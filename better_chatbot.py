from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

def create_chatbot_with_different_model():
    """Try a different, more reliable model"""
    # Try GPT-2 small instead of DialoGPT
    model_name = "gpt2"  # More stable than DialoGPT-small
    
    print(f"Loading model: {model_name}")
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(model_name)
    
    # Set pad token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    return model, tokenizer

def chat_with_gpt2():
    """Chat using GPT-2 with proper conversation formatting"""
    print("=== GPT-2 Chatbot ===")
    print("Talk to the bot! (type 'quit' to exit)")
    
    model, tokenizer = create_chatbot_with_different_model()
    conversation_history = ""
    
    for turn in range(10):
        try:
            user_input = input("You: ")
            if user_input.lower() == "quit":
                break
            
            # Format conversation properly
            if conversation_history:
                prompt = f"{conversation_history}\nHuman: {user_input}\nAssistant:"
            else:
                prompt = f"Human: {user_input}\nAssistant:"
            
            # Encode input
            input_ids = tokenizer.encode(prompt, return_tensors="pt")
            attention_mask = torch.ones(input_ids.shape, dtype=torch.long)
            
            # Generate response
            with torch.no_grad():
                output = model.generate(
                    input_ids,
                    attention_mask=attention_mask,
                    max_length=input_ids.shape[-1] + 50,
                    min_length=input_ids.shape[-1] + 5,
                    do_sample=True,
                    top_k=50,
                    top_p=0.95,
                    temperature=0.7,
                    repetition_penalty=1.2,
                    no_repeat_ngram_size=3,
                    pad_token_id=tokenizer.eos_token_id,
                    eos_token_id=tokenizer.eos_token_id
                )
            
            # Extract response
            response = tokenizer.decode(output[0], skip_special_tokens=True)
            
            # Extract just the assistant's response
            if "Assistant:" in response:
                bot_response = response.split("Assistant:")[-1].strip()
                # Clean up response - stop at next "Human:" if it appears
                if "Human:" in bot_response:
                    bot_response = bot_response.split("Human:")[0].strip()
            else:
                bot_response = "I'm not sure how to respond to that."
            
            # Clean up and validate response
            bot_response = bot_response.strip()
            if not bot_response or len(bot_response) < 2:
                bot_response = "Could you please rephrase that?"
            
            print("Bot:", bot_response)
            
            # Update conversation history (keep it manageable)
            conversation_history = f"{conversation_history}\nHuman: {user_input}\nAssistant: {bot_response}"
            
            # Trim history if it gets too long
            if len(conversation_history) > 500:
                lines = conversation_history.split('\n')
                conversation_history = '\n'.join(lines[-6:])  # Keep last 3 exchanges
                
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"An error occurred: {e}")
            continue

def simple_response_chatbot():
    """A very simple chatbot that doesn't maintain conversation history"""
    print("=== Simple Response Chatbot ===")
    print("Talk to the bot! (type 'quit' to exit)")
    
    model, tokenizer = create_chatbot_with_different_model()
    
    for turn in range(10):
        try:
            user_input = input("You: ")
            if user_input.lower() == "quit":
                break
            
            # Simple prompt format
            prompt = f"Question: {user_input}\nAnswer:"
            
            # Encode and generate
            input_ids = tokenizer.encode(prompt, return_tensors="pt")
            
            with torch.no_grad():
                output = model.generate(
                    input_ids,
                    max_length=input_ids.shape[-1] + 30,
                    do_sample=True,
                    top_k=50,
                    top_p=0.9,
                    temperature=0.8,
                    repetition_penalty=1.1,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            # Extract response
            response = tokenizer.decode(output[0], skip_special_tokens=True)
            
            if "Answer:" in response:
                bot_response = response.split("Answer:")[-1].strip()
                # Take only the first sentence/line
                bot_response = bot_response.split('\n')[0].split('.')[0] + '.'
            else:
                bot_response = "I'm not sure about that."
            
            print("Bot:", bot_response)
                
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"An error occurred: {e}")
            continue

if __name__ == "__main__":
    print("Choose chatbot version:")
    print("1. GPT-2 with conversation history")
    print("2. Simple response chatbot (no history)")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        chat_with_gpt2()
    elif choice == "2":
        simple_response_chatbot()
    else:
        print("Invalid choice. Using simple response chatbot.")
        simple_response_chatbot()
