from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import random
import copy
import json
from dataclasses import dataclass, asdict
from typing import List
import numpy as np

@dataclass
class SmartGenome:
    """Genome focused on making AI smarter"""
    # Model intelligence level
    model_name: str = "gpt2"
    intelligence_level: int = 1  # 1=basic, 2=medium, 3=large, 4=xl
    
    # Smart generation parameters
    temperature: float = 0.7
    top_k: int = 50
    top_p: float = 0.95
    repetition_penalty: float = 1.1
    
    # Intelligence boosters
    max_new_tokens: int = 50
    num_beams: int = 1  # Beam search for quality
    do_sample: bool = True
    length_penalty: float = 1.0
    
    # Smart response settings
    min_response_length: int = 20
    context_length: int = 512
    
    # Intelligence scores
    smartness_score: float = 0.0
    fitness: float = 0.0
    generation: int = 0
    id: str = ""

class SmartEvolution:
    """Evolution system to make AI progressively smarter"""
    
    def __init__(self, population_size: int = 6):
        self.population_size = population_size
        self.population: List[SmartGenome] = []
        self.generation = 0
        
        # Model progression (small to large)
        self.smart_models = [
            {"name": "gpt2", "level": 1, "description": "GPT-2 Small (124M params)"},
            {"name": "gpt2-medium", "level": 2, "description": "GPT-2 Medium (355M params)"},
            {"name": "gpt2-large", "level": 3, "description": "GPT-2 Large (774M params)"},
            {"name": "microsoft/DialoGPT-medium", "level": 2, "description": "DialoGPT Medium (355M params)"},
            {"name": "microsoft/DialoGPT-large", "level": 3, "description": "DialoGPT Large (774M params)"},
        ]
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Smart Evolution using: {self.device}")
        
        self.model_cache = {}
    
    def load_smart_model(self, model_name: str):
        """Load progressively smarter models"""
        if model_name not in self.model_cache:
            print(f"Loading smart model: {model_name}")
            
            try:
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                
                if self.device.type == "cuda":
                    model = AutoModelForCausalLM.from_pretrained(
                        model_name,
                        dtype=torch.float16,
                        device_map="auto"
                    )
                else:
                    model = AutoModelForCausalLM.from_pretrained(model_name)
                    model = model.to(self.device)
                
                if tokenizer.pad_token is None:
                    tokenizer.pad_token = tokenizer.eos_token
                
                self.model_cache[model_name] = {"model": model, "tokenizer": tokenizer}
                
                # Get model info
                model_info = next((m for m in self.smart_models if m["name"] == model_name), None)
                if model_info:
                    print(f"✅ Loaded: {model_info['description']}")
                
            except Exception as e:
                print(f"❌ Failed to load {model_name}: {e}")
                if model_name != "gpt2":
                    print("Falling back to GPT-2...")
                    return self.load_smart_model("gpt2")
                else:
                    raise e
        
        return self.model_cache[model_name]
    
    def create_smart_population(self):
        """Create initial population with varying intelligence levels"""
        print("Creating smart AI population...")
        
        for i in range(self.population_size):
            # Start with different intelligence levels
            model_choice = random.choice(self.smart_models[:3])  # Start with smaller models
            
            genome = SmartGenome(
                model_name=model_choice["name"],
                intelligence_level=model_choice["level"],
                temperature=random.uniform(0.4, 1.0),  # Lower temp for smarter responses
                top_k=random.randint(30, 80),
                top_p=random.uniform(0.8, 0.98),
                repetition_penalty=random.uniform(1.0, 1.3),
                max_new_tokens=random.randint(30, 80),
                num_beams=random.choice([1, 2, 3]),
                do_sample=random.choice([True, False]),
                length_penalty=random.uniform(0.8, 1.2),
                min_response_length=random.randint(15, 40),
                context_length=random.choice([256, 512, 768]),
                generation=0,
                id=f"gen0_smart{i}"
            )
            
            self.population.append(genome)
        
        print("Smart population created!")
    
    def generate_smart_response(self, genome: SmartGenome, question: str) -> str:
        """Generate response using smart parameters"""
        model_data = self.load_smart_model(genome.model_name)
        model = model_data["model"]
        tokenizer = model_data["tokenizer"]
        
        # Smart prompting
        if genome.intelligence_level >= 2:
            prompt = f"Question: {question}\n\nLet me think about this carefully and provide a detailed, intelligent response:\n\nAnswer:"
        else:
            prompt = f"Human: {question}\nAssistant:"
        
        # Encode with context limit
        input_ids = tokenizer.encode(prompt, return_tensors="pt").to(self.device)
        
        if input_ids.shape[-1] > genome.context_length:
            input_ids = input_ids[:, -genome.context_length:]
        
        # Smart generation parameters
        generation_kwargs = {
            "input_ids": input_ids,
            "max_new_tokens": genome.max_new_tokens,
            "min_length": input_ids.shape[-1] + genome.min_response_length,
            "temperature": genome.temperature,
            "top_k": genome.top_k,
            "top_p": genome.top_p,
            "repetition_penalty": genome.repetition_penalty,
            "length_penalty": genome.length_penalty,
            "do_sample": genome.do_sample,
            "pad_token_id": tokenizer.eos_token_id,
            "attention_mask": torch.ones(input_ids.shape, device=self.device)
        }
        
        # Use beam search for higher intelligence
        if genome.num_beams > 1:
            generation_kwargs["num_beams"] = genome.num_beams
            generation_kwargs["early_stopping"] = True
        
        with torch.no_grad():
            output = model.generate(**generation_kwargs)
        
        # Extract smart response
        response = tokenizer.decode(output[0], skip_special_tokens=True)
        
        if "Answer:" in response:
            smart_response = response.split("Answer:")[-1].strip()
        elif "Assistant:" in response:
            smart_response = response.split("Assistant:")[-1].strip()
        else:
            smart_response = response[len(tokenizer.decode(input_ids[0], skip_special_tokens=True)):].strip()
        
        # Clean up
        if "Human:" in smart_response:
            smart_response = smart_response.split("Human:")[0].strip()
        
        return smart_response.strip() if smart_response.strip() else "I need to think more about this."
    
    def evaluate_smartness(self, genome: SmartGenome, smart_questions: List[str]) -> float:
        """Evaluate how smart the AI responses are"""
        smartness_scores = []
        
        for question in smart_questions:
            try:
                response = self.generate_smart_response(genome, question)
                score = self.calculate_smartness_score(response, question)
                smartness_scores.append(score)
            except Exception as e:
                print(f"Error evaluating {genome.id}: {e}")
                smartness_scores.append(0.0)
        
        return np.mean(smartness_scores) if smartness_scores else 0.0
    
    def calculate_smartness_score(self, response: str, question: str) -> float:
        """Calculate how smart a response is"""
        if not response or len(response.strip()) < 10:
            return 0.0
        
        score = 0.0
        
        # Length and detail (smart responses are usually more detailed)
        if len(response) >= 50:
            score += 0.3
        if len(response) >= 100:
            score += 0.2
        
        # Smart vocabulary
        smart_words = [
            "analyze", "consider", "understand", "explain", "because", "therefore",
            "however", "furthermore", "specifically", "particularly", "essentially",
            "comprehensive", "complex", "sophisticated", "detailed", "thorough",
            "perspective", "approach", "methodology", "concept", "principle"
        ]
        
        smart_word_count = sum(1 for word in smart_words if word in response.lower())
        score += min(0.3, smart_word_count * 0.05)
        
        # Avoid dumb responses
        dumb_phrases = [
            "i don't know", "i'm not sure", "maybe", "i think", "probably",
            "i guess", "sort of", "kind of", "um", "uh"
        ]
        
        dumb_count = sum(1 for phrase in dumb_phrases if phrase in response.lower())
        score -= dumb_count * 0.1
        
        # Coherence and structure
        sentences = [s.strip() for s in response.split('.') if s.strip()]
        if len(sentences) >= 2:
            score += 0.2  # Multi-sentence responses
        
        # Proper endings
        if response.strip().endswith(('.', '!', '?')):
            score += 0.1
        
        # Question relevance
        question_words = set(question.lower().split())
        response_words = set(response.lower().split())
        relevance = len(question_words & response_words) / len(question_words) if question_words else 0
        score += relevance * 0.2
        
        return min(1.0, max(0.0, score))
    
    def evolve_smarter(self, smart_questions: List[str], num_generations: int = 4):
        """Evolve AI to become progressively smarter"""
        print(f"🧠 Starting Smart Evolution for {num_generations} generations...")
        
        for generation in range(num_generations):
            print(f"\n🚀 SMART GENERATION {generation + 1}")
            
            # Evaluate smartness
            for i, genome in enumerate(self.population):
                print(f"Testing smartness {i+1}/{len(self.population)}: {genome.id}")
                
                smartness = self.evaluate_smartness(genome, smart_questions)
                
                # Bonus for larger models (they're inherently smarter)
                model_bonus = genome.intelligence_level * 0.1
                
                genome.smartness_score = smartness
                genome.fitness = smartness + model_bonus
                
                model_info = next((m for m in self.smart_models if m["name"] == genome.model_name), {"description": "Unknown"})
                print(f"  Model: {model_info['description']}")
                print(f"  Smartness: {smartness:.3f}, Fitness: {genome.fitness:.3f}")
            
            # Show generation stats
            fitnesses = [g.fitness for g in self.population]
            smartness_scores = [g.smartness_score for g in self.population]
            
            print(f"\n📊 Generation {generation + 1} Results:")
            print(f"  Best Fitness: {max(fitnesses):.3f}")
            print(f"  Avg Fitness: {np.mean(fitnesses):.3f}")
            print(f"  Best Smartness: {max(smartness_scores):.3f}")
            print(f"  Avg Smartness: {np.mean(smartness_scores):.3f}")
            
            # Evolve to next generation
            if generation < num_generations - 1:
                self.create_smarter_generation()
        
        return self.get_smartest_ai()
    
    def create_smarter_generation(self):
        """Create next generation with focus on intelligence"""
        # Sort by fitness (smartness + model bonus)
        self.population.sort(key=lambda x: x.fitness, reverse=True)
        
        new_population = []
        
        # Keep the smartest (elitism)
        elite_count = max(1, self.population_size // 3)
        for i in range(elite_count):
            elite = copy.deepcopy(self.population[i])
            elite.generation = self.generation + 1
            elite.id = f"gen{self.generation + 1}_elite{i}"
            new_population.append(elite)
        
        # Create smarter offspring
        while len(new_population) < self.population_size:
            # Select smart parents
            parent1 = self.select_smart_parent()
            parent2 = self.select_smart_parent()
            
            # Create smart child
            child = self.smart_crossover(parent1, parent2)
            child = self.smart_mutation(child)
            
            child.generation = self.generation + 1
            child.id = f"gen{self.generation + 1}_smart{len(new_population)}"
            child.fitness = 0.0
            
            new_population.append(child)
        
        self.population = new_population
        self.generation += 1
    
    def select_smart_parent(self) -> SmartGenome:
        """Select parent based on smartness"""
        tournament_size = 3
        tournament = random.sample(self.population, min(tournament_size, len(self.population)))
        return max(tournament, key=lambda x: x.smartness_score)
    
    def smart_crossover(self, parent1: SmartGenome, parent2: SmartGenome) -> SmartGenome:
        """Crossover that favors smarter traits"""
        child = SmartGenome()
        
        # Choose smarter parent's model with higher probability
        if parent1.smartness_score > parent2.smartness_score:
            smart_parent = parent1
            prob = 0.7
        else:
            smart_parent = parent2
            prob = 0.7
        
        # Model inheritance (favor smarter parent)
        if random.random() < prob:
            child.model_name = smart_parent.model_name
            child.intelligence_level = smart_parent.intelligence_level
        else:
            other_parent = parent2 if smart_parent == parent1 else parent1
            child.model_name = other_parent.model_name
            child.intelligence_level = other_parent.intelligence_level
        
        # Parameter inheritance
        for param in ['temperature', 'top_k', 'top_p', 'repetition_penalty', 
                     'max_new_tokens', 'num_beams', 'length_penalty', 
                     'min_response_length', 'context_length']:
            if random.random() < prob:
                setattr(child, param, getattr(smart_parent, param))
            else:
                other_parent = parent2 if smart_parent == parent1 else parent1
                setattr(child, param, getattr(other_parent, param))
        
        child.do_sample = smart_parent.do_sample if random.random() < prob else (parent2 if smart_parent == parent1 else parent1).do_sample
        
        return child
    
    def smart_mutation(self, genome: SmartGenome, mutation_rate: float = 0.3) -> SmartGenome:
        """Mutation that tends toward smarter configurations"""
        mutated = copy.deepcopy(genome)
        
        # Model upgrade mutation (chance to upgrade to smarter model)
        if random.random() < 0.25:
            current_level = mutated.intelligence_level
            smarter_models = [m for m in self.smart_models if m["level"] > current_level]
            
            if smarter_models:
                upgrade = random.choice(smarter_models)
                mutated.model_name = upgrade["name"]
                mutated.intelligence_level = upgrade["level"]
                print(f"🧠 Intelligence upgrade: Level {current_level} -> Level {upgrade['level']}")
        
        # Smart parameter mutations
        if random.random() < mutation_rate:
            # Lower temperature for more focused responses
            mutated.temperature = max(0.1, min(1.2, mutated.temperature + random.gauss(0, 0.1)))
        
        if random.random() < mutation_rate:
            # Increase context for smarter responses
            mutated.context_length = random.choice([512, 768, 1024])
        
        if random.random() < mutation_rate:
            # Longer responses for more detail
            mutated.max_new_tokens = max(20, min(120, mutated.max_new_tokens + random.randint(-10, 20)))
        
        if random.random() < mutation_rate:
            # Beam search for quality
            mutated.num_beams = random.choice([1, 2, 3, 4])
        
        # Other mutations
        if random.random() < mutation_rate:
            mutated.top_k = max(10, min(100, mutated.top_k + random.randint(-10, 10)))
        
        if random.random() < mutation_rate:
            mutated.top_p = max(0.7, min(0.99, mutated.top_p + random.gauss(0, 0.05)))
        
        return mutated
    
    def get_smartest_ai(self) -> SmartGenome:
        """Get the smartest AI from population"""
        return max(self.population, key=lambda x: x.smartness_score)

def create_smart_questions() -> List[str]:
    """Create questions to test AI intelligence"""
    return [
        "Explain how artificial intelligence works in simple terms.",
        "What are the main differences between machine learning and traditional programming?",
        "How would you solve a complex problem step by step?",
        "What are the potential benefits and risks of AI technology?",
        "Describe the concept of neural networks and how they learn.",
        "What makes human intelligence different from artificial intelligence?",
        "How do you think AI will change society in the next decade?",
        "Explain the importance of data in machine learning systems.",
        "What ethical considerations should guide AI development?",
        "How would you explain consciousness and whether AI can achieve it?"
    ]

def main():
    """Main smart evolution experiment"""
    print("🧠 SMART AI EVOLUTION EXPERIMENT 🧠")
    print("This will evolve AI to become progressively smarter by:")
    print("- Upgrading to larger, more powerful models")
    print("- Optimizing parameters for intelligent responses")
    print("- Selecting for smartness and coherence")
    print()
    
    # Create smart evolution system
    evolution = SmartEvolution(population_size=5)  # Smaller for larger models
    evolution.create_smart_population()
    
    # Create smart test questions
    smart_questions = create_smart_questions()
    
    # Run smart evolution
    smartest_ai = evolution.evolve_smarter(smart_questions, num_generations=4)
    
    print(f"\n🎓 SMART EVOLUTION COMPLETE!")
    print(f"Smartest AI: {smartest_ai.id}")
    
    model_info = next((m for m in evolution.smart_models if m["name"] == smartest_ai.model_name), {"description": "Unknown"})
    print(f"Model: {model_info['description']}")
    print(f"Intelligence Level: {smartest_ai.intelligence_level}")
    print(f"Smartness Score: {smartest_ai.smartness_score:.3f}")
    print(f"Overall Fitness: {smartest_ai.fitness:.3f}")
    
    # Save the smartest AI
    smart_ai_data = asdict(smartest_ai)
    with open('smartest_ai.json', 'w') as f:
        json.dump(smart_ai_data, f, indent=2)
    
    print("Smartest AI saved to 'smartest_ai.json'")
    
    # Test the smartest AI
    print(f"\n🧠 Testing the smartest AI...")
    test_question = "Explain how machine learning works and why it's important."
    response = evolution.generate_smart_response(smartest_ai, test_question)
    
    print(f"Question: {test_question}")
    print(f"Smart AI Response: {response}")

if __name__ == "__main__":
    main()
