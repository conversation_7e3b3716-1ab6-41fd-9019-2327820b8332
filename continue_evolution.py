from cuda_evolution import CUDAEvolutionEngine, ChatbotGenome
import copy
import json

class ContinuousEvolution(CUDAEvolutionEngine):
    """Continue evolution from your best evolved chatbot"""
    
    def __init__(self, population_size: int = 10, model_name: str = "gpt2"):
        super().__init__(population_size, model_name)
        
    def load_evolved_population(self, best_genome_params: dict):
        """Start evolution from your best evolved genome"""
        print("Creating population based on evolved genome...")
        
        # Create the elite genome
        elite_genome = ChatbotGenome(**best_genome_params)
        elite_genome.generation = 0
        elite_genome.id = "evolved_elite"
        elite_genome.fitness = 0.0
        
        self.population = [elite_genome]
        
        # Create variations of the elite genome
        for i in range(1, self.population_size):
            # Create a mutated version of the elite
            variant = copy.deepcopy(elite_genome)
            variant = self.mutate(variant, mutation_rate=0.3)  # Higher mutation for exploration
            variant.id = f"evolved_variant_{i}"
            variant.fitness = 0.0
            
            self.population.append(variant)
        
        print(f"Population created with {self.population_size} variants of evolved genome!")
    
    def targeted_evolution(self, target_improvements: dict, num_generations: int = 5):
        """Evolve with specific targets in mind"""
        print(f"Starting targeted evolution for {num_generations} generations...")
        print(f"Target improvements: {target_improvements}")
        
        # Custom fitness function for targeted evolution
        original_fitness_func = self.calculate_fitness
        
        def targeted_fitness(response: str) -> float:
            base_fitness = original_fitness_func(response)
            
            # Add bonuses for target improvements
            bonus = 0.0
            
            if "length" in target_improvements:
                target_length = target_improvements["length"]
                length_bonus = max(0, 1.0 - abs(len(response) - target_length) / target_length)
                bonus += length_bonus * 0.2
            
            if "creativity" in target_improvements and target_improvements["creativity"]:
                # Bonus for more unique words
                words = response.lower().split()
                if len(words) > 0:
                    creativity_bonus = len(set(words)) / len(words)
                    bonus += creativity_bonus * 0.3
            
            if "coherence" in target_improvements and target_improvements["coherence"]:
                # Bonus for proper sentence structure
                if response.count('.') + response.count('!') + response.count('?') >= 1:
                    bonus += 0.2
            
            return min(1.0, base_fitness + bonus)
        
        # Temporarily replace fitness function
        self.calculate_fitness = targeted_fitness
        
        # Run evolution
        test_inputs = [
            "Hello", "How are you?", "What do you think about music?",
            "Tell me a joke", "What's your opinion?", "Can you help me?",
            "What's interesting?", "How do you feel?", "What do you like?", "Tell me more"
        ]
        
        best_genome = self.run_evolution(num_generations, test_inputs)
        
        # Restore original fitness function
        self.calculate_fitness = original_fitness_func
        
        return best_genome

def continue_from_best():
    """Continue evolution from your best evolved chatbot"""
    
    # Your best evolved genome parameters
    best_evolved_params = {
        'temperature': 1.0664064131832431,
        'top_k': 55,
        'top_p': 0.9257422967302524,
        'repetition_penalty': 1.0030921193878959,
        'max_response_length': 37,
        'fitness': 0.7318211598746082,
        'generation': 5,
        'id': 'gen5_elite0'
    }
    
    print("🧬 CONTINUING EVOLUTION FROM BEST GENOME 🧬")
    print(f"Starting from fitness: {best_evolved_params['fitness']:.3f}")
    
    # Create evolution engine
    evolution = ContinuousEvolution(population_size=12, model_name="gpt2")
    evolution.load_evolved_population(best_evolved_params)
    
    # Continue evolution
    test_inputs = [
        "Hello", "How are you?", "What do you think about music?",
        "Tell me a joke", "What's your opinion?", "Can you help me?",
        "What's interesting?", "How do you feel?", "What do you like?", "Tell me more"
    ]
    
    improved_genome = evolution.run_evolution(num_generations=5, test_inputs=test_inputs)
    
    print(f"\n🎉 EVOLUTION COMPLETE!")
    print(f"Original fitness: {best_evolved_params['fitness']:.3f}")
    print(f"New fitness: {improved_genome.fitness:.3f}")
    print(f"Improvement: {((improved_genome.fitness - best_evolved_params['fitness']) / best_evolved_params['fitness'] * 100):+.1f}%")
    
    # Save the improved genome
    improved_params = {
        'temperature': improved_genome.temperature,
        'top_k': improved_genome.top_k,
        'top_p': improved_genome.top_p,
        'repetition_penalty': improved_genome.repetition_penalty,
        'max_response_length': improved_genome.max_response_length,
        'fitness': improved_genome.fitness,
        'generation': improved_genome.generation,
        'id': improved_genome.id
    }
    
    with open('improved_genome.json', 'w') as f:
        json.dump(improved_params, f, indent=2)
    
    print("Improved genome saved to 'improved_genome.json'")
    
    return improved_genome

def targeted_improvement():
    """Evolve with specific improvement targets"""
    
    best_evolved_params = {
        'temperature': 1.0664064131832431,
        'top_k': 55,
        'top_p': 0.9257422967302524,
        'repetition_penalty': 1.0030921193878959,
        'max_response_length': 37,
        'fitness': 0.7318211598746082,
        'generation': 5,
        'id': 'gen5_elite0'
    }
    
    print("🎯 TARGETED EVOLUTION 🎯")
    print("What would you like to improve?")
    print("1. Longer, more detailed responses")
    print("2. More creative/diverse responses") 
    print("3. Better coherence and grammar")
    print("4. Custom target")
    
    choice = input("Enter choice (1-4): ").strip()
    
    if choice == "1":
        targets = {"length": 80, "coherence": True}
        print("Targeting longer, more coherent responses...")
    elif choice == "2":
        targets = {"creativity": True}
        print("Targeting more creative responses...")
    elif choice == "3":
        targets = {"coherence": True}
        print("Targeting better coherence...")
    else:
        targets = {"length": 50, "creativity": True, "coherence": True}
        print("Targeting overall improvement...")
    
    evolution = ContinuousEvolution(population_size=10, model_name="gpt2")
    evolution.load_evolved_population(best_evolved_params)
    
    improved_genome = evolution.targeted_evolution(targets, num_generations=5)
    
    print(f"\n🎯 TARGETED EVOLUTION COMPLETE!")
    print(f"Improved genome: {improved_genome.id}")
    print(f"New fitness: {improved_genome.fitness:.3f}")
    
    return improved_genome

def multi_objective_evolution():
    """Evolve for multiple objectives simultaneously"""
    
    class MultiObjectiveEvolution(ContinuousEvolution):
        def calculate_fitness(self, response: str) -> float:
            """Multi-objective fitness function"""
            if not response or len(response.strip()) == 0:
                return 0.0
            
            # Multiple objectives with weights
            objectives = {}
            
            # Length objective (prefer 40-60 characters)
            length = len(response)
            if 40 <= length <= 60:
                objectives['length'] = 1.0
            else:
                objectives['length'] = max(0.0, 1.0 - abs(length - 50) / 50.0)
            
            # Diversity objective
            words = response.lower().split()
            if len(words) > 0:
                objectives['diversity'] = len(set(words)) / len(words)
            else:
                objectives['diversity'] = 0.0
            
            # Coherence objective
            objectives['coherence'] = 1.0 if response.strip().endswith(('.', '!', '?')) else 0.3
            
            # Engagement objective (avoid boring responses)
            boring_phrases = ["i'm not sure", "i don't know", "maybe", "perhaps"]
            objectives['engagement'] = 1.0
            for phrase in boring_phrases:
                if phrase in response.lower():
                    objectives['engagement'] = 0.4
                    break
            
            # Weighted combination
            weights = {'length': 0.25, 'diversity': 0.25, 'coherence': 0.25, 'engagement': 0.25}
            
            fitness = sum(objectives[obj] * weights[obj] for obj in objectives)
            return fitness
    
    best_evolved_params = {
        'temperature': 1.0664064131832431,
        'top_k': 55,
        'top_p': 0.9257422967302524,
        'repetition_penalty': 1.0030921193878959,
        'max_response_length': 37,
        'fitness': 0.7318211598746082,
        'generation': 5,
        'id': 'gen5_elite0'
    }
    
    print("🎯 MULTI-OBJECTIVE EVOLUTION 🎯")
    print("Optimizing for: Length + Diversity + Coherence + Engagement")
    
    evolution = MultiObjectiveEvolution(population_size=15, model_name="gpt2")
    evolution.load_evolved_population(best_evolved_params)
    
    test_inputs = [
        "Hello", "How are you?", "What do you think about music?",
        "Tell me a joke", "What's your opinion?", "Can you help me?",
        "What's interesting?", "How do you feel?", "What do you like?", "Tell me more"
    ]
    
    improved_genome = evolution.run_evolution(num_generations=7, test_inputs=test_inputs)
    
    print(f"\n🎯 MULTI-OBJECTIVE EVOLUTION COMPLETE!")
    print(f"New fitness: {improved_genome.fitness:.3f}")
    
    return improved_genome

def main():
    """Main menu for continuing evolution"""
    print("🧬 EVOLUTION IMPROVEMENT MENU 🧬")
    print("How would you like to improve your AI?")
    print()
    print("1. Continue evolution from best genome (general improvement)")
    print("2. Targeted evolution (specific improvements)")
    print("3. Multi-objective evolution (balanced improvement)")
    print("4. Show current best genome")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == "1":
        continue_from_best()
    elif choice == "2":
        targeted_improvement()
    elif choice == "3":
        multi_objective_evolution()
    elif choice == "4":
        best_params = {
            'temperature': 1.0664064131832431,
            'top_k': 55,
            'top_p': 0.9257422967302524,
            'repetition_penalty': 1.0030921193878959,
            'max_response_length': 37,
            'fitness': 0.7318211598746082,
            'generation': 5,
            'id': 'gen5_elite0'
        }
        print("\n🧬 CURRENT BEST GENOME:")
        for key, value in best_params.items():
            print(f"  {key}: {value}")
    else:
        print("Invalid choice. Starting general evolution...")
        continue_from_best()

if __name__ == "__main__":
    main()
